#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pyfast_trader_elite as ft
import time

class RealStrategy(ft.Strategy):
    """实际的Python策略示例"""
    
    def __init__(self):
        """初始化策略"""
        super().__init__()
        self.ctx = None
        self.instrument_id = "BTC/USDT"
        self.exchange_id = ft.data_model.BINANCE
        self.last_bid_price = 0.0
        self.last_ask_price = 0.0
        self.order_count = 0
        self.position = 0.0
        self.is_initialized = False
        print("RealStrategy initialized")
    
    def on_start(self, ctx):
        """策略启动回调"""
        print("Strategy on_start called")
        self.ctx = ctx
        self.is_initialized = True
        
        # 订阅行情
        self._subscribe_market_data()
        
        # 注册定时器，每5秒执行一次
        if hasattr(self.ctx, 'register_timer'):
            timer_id = self.ctx.register_timer(5000, self._on_timer, True)
            print(f"Registered timer with ID: {timer_id}")
        else:
            print("Context does not have register_timer method")
        
        return 0
    
    def _subscribe_market_data(self):
        """订阅行情数据"""
        if not self.ctx:
            print("Context not initialized")
            return
        
        print(f"Subscribing to {self.instrument_id} on {self.exchange_id}")
        
        # 创建订阅请求
        sub_field = ft.data_model.MdSubCodeField()
        sub_field.md_id = 1  # 假设MD ID为1
        sub_field.sub_code = [self.instrument_id]
        
        # 发送订阅请求
        if hasattr(self.ctx, 'subscribe'):
            self.ctx.subscribe(sub_field)
            print("Subscription request sent")
        else:
            print("Context does not have subscribe method")
    
    def _on_timer(self, timer_id):
        """定时器回调"""
        print(f"Timer triggered: {timer_id}")
        
        if not self.is_initialized or not self.ctx:
            print("Strategy not initialized")
            return
        
        if self.last_bid_price <= 0 or self.last_ask_price <= 0:
            print("No valid market data yet")
            return
        
        # 简单的做市策略：在买一价下方和卖一价上方挂单
        self._place_orders()
    
    def on_depth_data(self, depth_data):
        """行情数据回调"""
        if not depth_data:
            print("Received empty depth data")
            return
        
        print(f"Received depth data for {depth_data.instrument_id}")
        print(f"Bid: {depth_data.bid_price[0]}, Ask: {depth_data.ask_price[0]}")
        
        self.last_bid_price = depth_data.bid_price[0]
        self.last_ask_price = depth_data.ask_price[0]
        
        # 如果有足够的行情数据，尝试下单
        if self.last_bid_price > 0 and self.last_ask_price > 0:
            self._place_orders()
    
    def on_transaction_data(self, transaction_data):
        """成交数据回调"""
        if not transaction_data:
            print("Received empty transaction data")
            return
        
        print(f"Received transaction data for {transaction_data.instrument_id}")
        print(f"Price: {transaction_data.price}, Volume: {transaction_data.volume}")
    
    def on_kline_data(self, kline_data):
        """K线数据回调"""
        if not kline_data:
            print("Received empty kline data")
            return
        
        print(f"Received kline data for {kline_data.instrument_id}")
        print(f"Open: {kline_data.open}, Close: {kline_data.close}")
    
    def on_order(self, order_data):
        """订单状态回调"""
        if not order_data:
            print("Received empty order data")
            return
        
        print(f"Received order update for {order_data.instrument_id}")
        print(f"Order ID: {order_data.order_id}, Status: {order_data.order_status}")
    
    def on_trade(self, trade_data):
        """成交回报回调"""
        if not trade_data:
            print("Received empty trade data")
            return
        
        print(f"Received trade for {trade_data.instrument_id}")
        print(f"Order ID: {trade_data.order_id}, Price: {trade_data.last_price}, Volume: {trade_data.volume}")
        
        # 更新持仓
        if trade_data.direction == ft.data_model.BUY:
            self.position += trade_data.volume
        else:
            self.position -= trade_data.volume
        
        print(f"Current position: {self.position}")
    
    def _place_orders(self):
        """下单逻辑"""
        if not self.ctx:
            print("Context not initialized")
            return
        
        if self.last_bid_price <= 0 or self.last_ask_price <= 0:
            print("No valid market data yet")
            return
        
        # 创建买单
        buy_order = ft.data_model.OrderInputField()
        buy_order.instrument_id = self.instrument_id
        buy_order.instrument_name = self.instrument_id
        buy_order.exchange_id = self.exchange_id
        buy_order.ins_type = ft.data_model.SPOT
        buy_order.price = self.last_bid_price - 10  # 买一价下方10个点
        buy_order.volume = 0.01  # 小数量
        buy_order.direction = ft.data_model.BUY
        buy_order.offset = ft.data_model.OPEN
        buy_order.pricetype = ft.data_model.LIMIT
        buy_order.volume_condition = ft.data_model.ANY
        buy_order.time_condition = ft.data_model.GTC
        buy_order.portfolio_id = 1
        buy_order.trading_account_id = 1
        buy_order.strategy_instance_id = 1
        
        # 创建卖单
        sell_order = ft.data_model.OrderInputField()
        sell_order.instrument_id = self.instrument_id
        sell_order.instrument_name = self.instrument_id
        sell_order.exchange_id = self.exchange_id
        sell_order.ins_type = ft.data_model.SPOT
        sell_order.price = self.last_ask_price + 10  # 卖一价上方10个点
        sell_order.volume = 0.01  # 小数量
        sell_order.direction = ft.data_model.SELL
        sell_order.offset = ft.data_model.OPEN
        sell_order.pricetype = ft.data_model.LIMIT
        sell_order.volume_condition = ft.data_model.ANY
        sell_order.time_condition = ft.data_model.GTC
        sell_order.portfolio_id = 1
        sell_order.trading_account_id = 1
        sell_order.strategy_instance_id = 1
        
        # 发送订单
        if hasattr(self.ctx, 'insert_order'):
            buy_order_id = self.ctx.insert_order(buy_order)
            sell_order_id = self.ctx.insert_order(sell_order)
            
            print(f"Placed buy order: {buy_order_id}, price: {buy_order.price}")
            print(f"Placed sell order: {sell_order_id}, price: {sell_order.price}")
            
            self.order_count += 2
            
            # 如果下了10个订单，就全部撤单
            if self.order_count >= 10:
                self._cancel_all_orders()
        else:
            print("Context does not have insert_order method")
    
    def _cancel_all_orders(self):
        """撤销所有订单"""
        if not self.ctx:
            print("Context not initialized")
            return
        
        # 创建撤单请求
        cancel_req = ft.data_model.OrderActionField()
        cancel_req.instrument_id = self.instrument_id
        cancel_req.instrument_name = self.instrument_id
        cancel_req.exchange_id = self.exchange_id
        cancel_req.trading_account_id = 1
        cancel_req.strategy_instance_id = 1
        
        # 发送撤单请求
        if hasattr(self.ctx, 'cancel_order_all'):
            self.ctx.cancel_order_all(cancel_req)
            print("Cancelled all orders")
            self.order_count = 0
        else:
            print("Context does not have cancel_order_all method")
