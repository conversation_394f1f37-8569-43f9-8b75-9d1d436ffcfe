#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pyfast_trader_elite as ft

class SimpleStrategy(ft.Strategy):
    """简单的Python策略示例"""
    
    def __init__(self):
        """初始化策略"""
        super().__init__()
        print("SimpleStrategy initialized")
    
    def on_start(self, ctx):
        """策略启动回调"""
        print("Strategy on_start called")
        return 0
    
    def on_depth_data(self, depth_data):
        """行情数据回调"""
        print("Received depth data")
        return 0
    
    def on_order(self, order_data):
        """订单状态回调"""
        print("Received order update")
        return 0
    
    def on_trade(self, trade_data):
        """成交回报回调"""
        print("Received trade")
        return 0
