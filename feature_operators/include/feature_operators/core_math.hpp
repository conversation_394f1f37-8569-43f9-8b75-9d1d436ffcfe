// #ifndef FEATURE_OPERATORS_CORE_MATH_HPP
// #define FEATURE_OPERATORS_CORE_MATH_HPP

// #include "feature_operators/types.hpp" // Added
// #include <Eigen/Dense> // Kept for other Eigen types if any, types.hpp includes it too

// namespace feature_operators {

// DataFrame Add(const DataFrame& a, const DataFrame& b);
// DataFrame Minus(const DataFrame& a, const DataFrame& b);
// DataFrame Multiply(const DataFrame& a, const DataFrame& b);
// DataFrame Divide(const DataFrame& a, const DataFrame& b);
// DataFrame Sqrt(const DataFrame& a);
// DataFrame log(const DataFrame& a); // lowercase 'l'
// DataFrame inv(const DataFrame& a);
// DataFrame Floor(const DataFrame& a);
// DataFrame Ceil(const DataFrame& a);
// DataFrame Round(const DataFrame& a);
// DataFrame Abs(const DataFrame& a);
// DataFrame Log(const DataFrame& a); // capital 'L'
// DataFrame Sign(const DataFrame& a);
// DataFrame Reverse(const DataFrame& a);
// DataFrame Power(const DataFrame& base, double exponent);
// DataFrame Exp(const DataFrame& a);
// DataFrame SignedPower(const DataFrame& base, double exponent);
// DataFrame Softsign(const DataFrame& a);

// } // namespace feature_operators
// #endif // FEATURE_OPERATORS_CORE_MATH_HPP

#include "feature_operators/types.hpp"     // Should define DataFrame, or this file now provides generic Eigen ops
#include <cmath>                           // For std::log, std::sqrt (used in Log lambda)
#include <limits>                          // For std::numeric_limits
#include <Eigen/Dense>                     // For Eigen::ArrayBase and core Eigen functionalities

namespace feature_operators {

// Binary operations

template<typename DerivedA, typename DerivedB>
auto Add(const Eigen::ArrayBase<DerivedA>& a, const Eigen::ArrayBase<DerivedB>& b) {
    return a + b;
}

template<typename DerivedA, typename DerivedB>
auto Minus(const Eigen::ArrayBase<DerivedA>& a, const Eigen::ArrayBase<DerivedB>& b) {
    return a - b;
}

template<typename DerivedA, typename DerivedB>
auto Multiply(const Eigen::ArrayBase<DerivedA>& a, const Eigen::ArrayBase<DerivedB>& b) {
    return a * b; // Coefficient-wise product for Eigen arrays
}

template<typename DerivedA, typename DerivedB>
auto Divide(const Eigen::ArrayBase<DerivedA>& a, const Eigen::ArrayBase<DerivedB>& b) {
    return a / b; // Coefficient-wise division for Eigen arrays
}

// Unary operations

template<typename Derived>
auto Sqrt(const Eigen::ArrayBase<Derived>& a) {
    return a.sqrt();
}

template<typename Derived>
auto log(const Eigen::ArrayBase<Derived>& a) { // lowercase 'l', natural logarithm
    return a.log();
}

template<typename Derived>
auto inv(const Eigen::ArrayBase<Derived>& a) {
    // Assuming 'a' contains floating point numbers or types that can be divided by 1.0.
    // Given other functions like log, 'a' is likely float/double.
    return static_cast<typename Derived::Scalar>(1.0) / a;
}

template<typename Derived>
auto Floor(const Eigen::ArrayBase<Derived>& a) {
    return a.floor();
}

template<typename Derived>
auto Ceil(const Eigen::ArrayBase<Derived>& a) {
    return a.ceil();
}

template<typename Derived>
auto Round(const Eigen::ArrayBase<Derived>& a) {
    return a.round();
}

// template<typename Derived>
// auto Abs(const Eigen::ArrayBase<Derived>& a) {
//     return a.abs();
// }
static inline DataFrame Abs(const DataFrame& a){
      return a.abs();
}
template<typename Derived>
auto Log(const Eigen::ArrayBase<Derived>& a) { // capital 'L', natural log with non-positive check
    // The scalar type of the ArrayBase 'a' should be compatible with 'x' in the lambda.
    return a.unaryExpr([](typename Derived::Scalar x) {
        return (x > static_cast<typename Derived::Scalar>(0))
                   ? std::log(x) // std::log typically operates on double or float
                   : std::numeric_limits<typename Derived::Scalar>::quiet_NaN();
    });
}

template<typename Derived>
auto Sign(const Eigen::ArrayBase<Derived>& a) {
    return a.sign();
}

template<typename Derived>
auto Reverse(const Eigen::ArrayBase<Derived>& a) {
    return -a; // Unary minus operator
}

template<typename Derived>
auto Power(const Eigen::ArrayBase<Derived>& base, double exponent) {
    // Eigen's pow() function for arrays takes the exponent as the argument.
    // The scalar type of 'base' should be compatible with raising to a double 'exponent'.
    return base.pow((int)exponent);
}

template<typename Derived>
auto Exp(const Eigen::ArrayBase<Derived>& a) {
    return a.exp(); // Coefficient-wise exponential
}

template<typename Derived>
auto SignedPower(const Eigen::ArrayBase<Derived>& base, double exponent) {
    // base.sign() * base.abs().pow(exponent)
    // This leverages other templated Eigen expressions.
    return base.sign() * base.abs().pow(exponent);
}

template<typename Derived>
auto Softsign(const Eigen::ArrayBase<Derived>& a) {
    // Implements: a / (1.0 + a.sqrt().abs())
    // This matches the original C++ code's implementation line and the comment about
    // "original python code is: Divide(data, (1 + Abs(Sqrt(data))))".
    // a.sqrt() on negative values produces NaN, which is propagated by .abs().
    using Scalar = typename Derived::Scalar;
    return a / (static_cast<Scalar>(1.0) + a.sqrt().abs());
}

} // namespace feature_operators