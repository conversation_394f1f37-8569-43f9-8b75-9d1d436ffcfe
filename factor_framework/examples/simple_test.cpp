/**
 * 简单的因子框架测试程序
 * 测试因子注册和计算功能
 */

#include "factor_framework/factor_framework.hpp"
#include "generated_factors/all_factors.hpp"
#include <iostream>
#include <iomanip>

using namespace factor_framework;

int main() {
    try {
        std::cout << "=== 因子框架简单测试 ===" << std::endl;

        // 1. 初始化框架
        std::cout << "\n1. 初始化框架..." << std::endl;
        FACTOR_FRAMEWORK_INIT();
        
        // 2. 初始化生成的因子
        std::cout << "\n2. 初始化生成的因子..." << std::endl;
        generated_factors::initialize_all_factors();

        // 3. 检查注册列表
        std::cout << "\n3. 检查因子注册列表..." << std::endl;
        auto& registration_list = get_factor_registration_list();
        std::cout << "注册列表中的因子数量: " << registration_list.size() << std::endl;

        // 4. 创建因子管理器
        std::cout << "\n4. 创建因子管理器..." << std::endl;
        auto factor_manager = std::make_shared<factor_framework::factor_manager>();

        // 5. 手动注册因子
        // std::cout << "\n5. 手动注册因子..." << std::endl;
        // int registered_count = 0;
        // for (auto& creator : registration_list) {
        //     try {
        //         auto factor = creator();
        //         if (factor) {
        //             std::cout << "注册因子: ID=" << factor->get_id() 
        //                       << ", Name=" << factor->get_name() << std::endl;
        //             if (factor_manager->register_factor(std::move(factor))) {
        //                 registered_count++;
        //             }
        //         }
        //     } catch (const std::exception& e) {
        //         std::cerr << "注册因子时出错: " << e.what() << std::endl;
        //     }
        // }
        // std::cout << "成功注册 " << registered_count << " 个因子" << std::endl;
        int registered_count = generated_factors::register_all_factors_to_manager(factor_manager);
        std::cout << "成功注册 " << registered_count << " 个因子" << std::endl;

        // 6. 创建数据接口并加载测试数据
        std::cout << "\n6. 创建测试数据..." << std::endl;
        auto data_interface = std::make_shared<factor_framework::data_interface>();
        data_interface->create_standard_market_data(4000, 500);  // 20天，10只股票

        // 7. 选择因子进行测试
        std::cout << "\n7. 选择因子进行测试..." << std::endl;
        std::vector<int> test_factor_ids ;  // 只测试第一个因子
        auto all_factor = factor_manager->get_all_factor_ids();
        for(auto id : all_factor){
            test_factor_ids.push_back(id);
        }
        // test_factor_ids.push_back(21);
        //  test_factor_ids.push_back(22);
        // test_factor_ids.push_back(23);
        // test_factor_ids.push_back(24);
        // test_factor_ids.push_back(25);
        factor_manager->select_factors(test_factor_ids);
        std::cout << "选中的因子数量: " << factor_manager->get_selected_factor_count() << std::endl;

        // 8. 获取选中因子的详细信息
        std::cout << "\n8. 因子详细信息:" << std::endl;
        auto selected_ids = factor_manager->get_selected_factor_ids();
        for (const auto& factor_id : selected_ids) {
            auto factor = factor_manager->get_factor(factor_id);
            if (factor) {
                std::cout << "  因子ID: " << factor->get_id() << std::endl;
                std::cout << "  因子名称: " << factor->get_name() << std::endl;
                std::cout << "  因子公式: " << factor->get_formula() << std::endl;
                
                auto required_fields = factor->get_required_fields();
                std::cout << "  所需字段: ";
                for (size_t i = 0; i < required_fields.size(); ++i) {
                    if (i > 0) std::cout << ", ";
                    std::cout << required_fields[i];
                }
                std::cout << std::endl;
            }
        }

        // 9. 测试因子计算
        std::cout << "\n9. 测试因子计算..." << std::endl;
        for(auto id : selected_ids){
            auto factor = factor_manager->get_factor(id);
            if (factor) {
                try {
                    std::cout<<"id:"<<id<<std::endl;
                    auto data_map = data_interface->get_all_data();
                    auto result = factor->calculate(data_map);
                    std::cout << "计算成功！结果维度: " << result.rows() << "×" << result.cols() << std::endl;
                } catch (const std::exception& e) {
                    std::cerr << "计算因子时出错: " << e.what() << std::endl;
                }
            }
        }

        // 10. 清理
        std::cout << "\n10. 清理资源..." << std::endl;
        FACTOR_FRAMEWORK_CLEANUP();

        std::cout << "\n=== 测试完成 ===" << std::endl;
        return 0;

    } catch (const std::exception& e) {
        std::cerr << "测试过程中发生异常: " << e.what() << std::endl;
        return 1;
    }
}
