# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.29

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/git/factor_framework

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/git/factor_framework/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/factor_framework_lib.dir/all
all: CMakeFiles/architecture_demo.dir/all
all: CMakeFiles/simple_test.dir/all
all: feature_operators_build/all
all: generated_factors/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall: feature_operators_build/preinstall
preinstall: generated_factors/preinstall
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/factor_framework_lib.dir/clean
clean: CMakeFiles/architecture_demo.dir/clean
clean: CMakeFiles/simple_test.dir/clean
clean: feature_operators_build/clean
clean: generated_factors/clean
.PHONY : clean

#=============================================================================
# Directory level rules for directory feature_operators_build

# Recursive "all" directory target.
feature_operators_build/all: feature_operators_build/CMakeFiles/feature_ops_lib.dir/all
feature_operators_build/all: feature_operators_build/CMakeFiles/core_math_tests.dir/all
feature_operators_build/all: feature_operators_build/CMakeFiles/data_utils_tests.dir/all
feature_operators_build/all: feature_operators_build/CMakeFiles/logical_ops_tests.dir/all
feature_operators_build/all: feature_operators_build/CMakeFiles/comparison_ops_tests.dir/all
feature_operators_build/all: feature_operators_build/CMakeFiles/reduction_ops_tests.dir/all
feature_operators_build/all: feature_operators_build/CMakeFiles/panel_ops_tests.dir/all
feature_operators_build/all: feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/all
feature_operators_build/all: feature_operators_build/CMakeFiles/group_ops_tests.dir/all
.PHONY : feature_operators_build/all

# Recursive "preinstall" directory target.
feature_operators_build/preinstall:
.PHONY : feature_operators_build/preinstall

# Recursive "clean" directory target.
feature_operators_build/clean: feature_operators_build/CMakeFiles/feature_ops_lib.dir/clean
feature_operators_build/clean: feature_operators_build/CMakeFiles/core_math_tests.dir/clean
feature_operators_build/clean: feature_operators_build/CMakeFiles/data_utils_tests.dir/clean
feature_operators_build/clean: feature_operators_build/CMakeFiles/logical_ops_tests.dir/clean
feature_operators_build/clean: feature_operators_build/CMakeFiles/comparison_ops_tests.dir/clean
feature_operators_build/clean: feature_operators_build/CMakeFiles/reduction_ops_tests.dir/clean
feature_operators_build/clean: feature_operators_build/CMakeFiles/panel_ops_tests.dir/clean
feature_operators_build/clean: feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/clean
feature_operators_build/clean: feature_operators_build/CMakeFiles/group_ops_tests.dir/clean
.PHONY : feature_operators_build/clean

#=============================================================================
# Directory level rules for directory generated_factors

# Recursive "all" directory target.
generated_factors/all: generated_factors/CMakeFiles/generated_factors.dir/all
.PHONY : generated_factors/all

# Recursive "preinstall" directory target.
generated_factors/preinstall:
.PHONY : generated_factors/preinstall

# Recursive "clean" directory target.
generated_factors/clean: generated_factors/CMakeFiles/generated_factors.dir/clean
.PHONY : generated_factors/clean

#=============================================================================
# Target rules for target CMakeFiles/factor_framework_lib.dir

# All Build rule for target.
CMakeFiles/factor_framework_lib.dir/all: feature_operators_build/CMakeFiles/feature_ops_lib.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/factor_framework_lib.dir/build.make CMakeFiles/factor_framework_lib.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/factor_framework_lib.dir/build.make CMakeFiles/factor_framework_lib.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/git/factor_framework/build/CMakeFiles --progress-num=9,10,11,12,13,14 "Built target factor_framework_lib"
.PHONY : CMakeFiles/factor_framework_lib.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/factor_framework_lib.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/factor_framework/build/CMakeFiles 17
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/factor_framework_lib.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/factor_framework/build/CMakeFiles 0
.PHONY : CMakeFiles/factor_framework_lib.dir/rule

# Convenience name for target.
factor_framework_lib: CMakeFiles/factor_framework_lib.dir/rule
.PHONY : factor_framework_lib

# clean rule for target.
CMakeFiles/factor_framework_lib.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/factor_framework_lib.dir/build.make CMakeFiles/factor_framework_lib.dir/clean
.PHONY : CMakeFiles/factor_framework_lib.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/architecture_demo.dir

# All Build rule for target.
CMakeFiles/architecture_demo.dir/all: CMakeFiles/factor_framework_lib.dir/all
CMakeFiles/architecture_demo.dir/all: feature_operators_build/CMakeFiles/feature_ops_lib.dir/all
CMakeFiles/architecture_demo.dir/all: generated_factors/CMakeFiles/generated_factors.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/architecture_demo.dir/build.make CMakeFiles/architecture_demo.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/architecture_demo.dir/build.make CMakeFiles/architecture_demo.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/git/factor_framework/build/CMakeFiles --progress-num=1,2 "Built target architecture_demo"
.PHONY : CMakeFiles/architecture_demo.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/architecture_demo.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/factor_framework/build/CMakeFiles 51
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/architecture_demo.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/factor_framework/build/CMakeFiles 0
.PHONY : CMakeFiles/architecture_demo.dir/rule

# Convenience name for target.
architecture_demo: CMakeFiles/architecture_demo.dir/rule
.PHONY : architecture_demo

# clean rule for target.
CMakeFiles/architecture_demo.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/architecture_demo.dir/build.make CMakeFiles/architecture_demo.dir/clean
.PHONY : CMakeFiles/architecture_demo.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/simple_test.dir

# All Build rule for target.
CMakeFiles/simple_test.dir/all: CMakeFiles/factor_framework_lib.dir/all
CMakeFiles/simple_test.dir/all: feature_operators_build/CMakeFiles/feature_ops_lib.dir/all
CMakeFiles/simple_test.dir/all: generated_factors/CMakeFiles/generated_factors.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/simple_test.dir/build.make CMakeFiles/simple_test.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/simple_test.dir/build.make CMakeFiles/simple_test.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/git/factor_framework/build/CMakeFiles --progress-num=66,67 "Built target simple_test"
.PHONY : CMakeFiles/simple_test.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/simple_test.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/factor_framework/build/CMakeFiles 51
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/simple_test.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/factor_framework/build/CMakeFiles 0
.PHONY : CMakeFiles/simple_test.dir/rule

# Convenience name for target.
simple_test: CMakeFiles/simple_test.dir/rule
.PHONY : simple_test

# clean rule for target.
CMakeFiles/simple_test.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/simple_test.dir/build.make CMakeFiles/simple_test.dir/clean
.PHONY : CMakeFiles/simple_test.dir/clean

#=============================================================================
# Target rules for target feature_operators_build/CMakeFiles/feature_ops_lib.dir

# All Build rule for target.
feature_operators_build/CMakeFiles/feature_ops_lib.dir/all:
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/feature_ops_lib.dir/build.make feature_operators_build/CMakeFiles/feature_ops_lib.dir/depend
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/feature_ops_lib.dir/build.make feature_operators_build/CMakeFiles/feature_ops_lib.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/git/factor_framework/build/CMakeFiles --progress-num=15,16,17,18,19,20,21,22,23,24,25 "Built target feature_ops_lib"
.PHONY : feature_operators_build/CMakeFiles/feature_ops_lib.dir/all

# Build rule for subdir invocation for target.
feature_operators_build/CMakeFiles/feature_ops_lib.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/factor_framework/build/CMakeFiles 11
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 feature_operators_build/CMakeFiles/feature_ops_lib.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/factor_framework/build/CMakeFiles 0
.PHONY : feature_operators_build/CMakeFiles/feature_ops_lib.dir/rule

# Convenience name for target.
feature_ops_lib: feature_operators_build/CMakeFiles/feature_ops_lib.dir/rule
.PHONY : feature_ops_lib

# clean rule for target.
feature_operators_build/CMakeFiles/feature_ops_lib.dir/clean:
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/feature_ops_lib.dir/build.make feature_operators_build/CMakeFiles/feature_ops_lib.dir/clean
.PHONY : feature_operators_build/CMakeFiles/feature_ops_lib.dir/clean

#=============================================================================
# Target rules for target feature_operators_build/CMakeFiles/core_math_tests.dir

# All Build rule for target.
feature_operators_build/CMakeFiles/core_math_tests.dir/all: feature_operators_build/CMakeFiles/feature_ops_lib.dir/all
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/core_math_tests.dir/build.make feature_operators_build/CMakeFiles/core_math_tests.dir/depend
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/core_math_tests.dir/build.make feature_operators_build/CMakeFiles/core_math_tests.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/git/factor_framework/build/CMakeFiles --progress-num=5,6 "Built target core_math_tests"
.PHONY : feature_operators_build/CMakeFiles/core_math_tests.dir/all

# Build rule for subdir invocation for target.
feature_operators_build/CMakeFiles/core_math_tests.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/factor_framework/build/CMakeFiles 13
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 feature_operators_build/CMakeFiles/core_math_tests.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/factor_framework/build/CMakeFiles 0
.PHONY : feature_operators_build/CMakeFiles/core_math_tests.dir/rule

# Convenience name for target.
core_math_tests: feature_operators_build/CMakeFiles/core_math_tests.dir/rule
.PHONY : core_math_tests

# clean rule for target.
feature_operators_build/CMakeFiles/core_math_tests.dir/clean:
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/core_math_tests.dir/build.make feature_operators_build/CMakeFiles/core_math_tests.dir/clean
.PHONY : feature_operators_build/CMakeFiles/core_math_tests.dir/clean

#=============================================================================
# Target rules for target feature_operators_build/CMakeFiles/data_utils_tests.dir

# All Build rule for target.
feature_operators_build/CMakeFiles/data_utils_tests.dir/all: feature_operators_build/CMakeFiles/feature_ops_lib.dir/all
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/data_utils_tests.dir/build.make feature_operators_build/CMakeFiles/data_utils_tests.dir/depend
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/data_utils_tests.dir/build.make feature_operators_build/CMakeFiles/data_utils_tests.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/git/factor_framework/build/CMakeFiles --progress-num=7,8 "Built target data_utils_tests"
.PHONY : feature_operators_build/CMakeFiles/data_utils_tests.dir/all

# Build rule for subdir invocation for target.
feature_operators_build/CMakeFiles/data_utils_tests.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/factor_framework/build/CMakeFiles 13
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 feature_operators_build/CMakeFiles/data_utils_tests.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/factor_framework/build/CMakeFiles 0
.PHONY : feature_operators_build/CMakeFiles/data_utils_tests.dir/rule

# Convenience name for target.
data_utils_tests: feature_operators_build/CMakeFiles/data_utils_tests.dir/rule
.PHONY : data_utils_tests

# clean rule for target.
feature_operators_build/CMakeFiles/data_utils_tests.dir/clean:
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/data_utils_tests.dir/build.make feature_operators_build/CMakeFiles/data_utils_tests.dir/clean
.PHONY : feature_operators_build/CMakeFiles/data_utils_tests.dir/clean

#=============================================================================
# Target rules for target feature_operators_build/CMakeFiles/logical_ops_tests.dir

# All Build rule for target.
feature_operators_build/CMakeFiles/logical_ops_tests.dir/all: feature_operators_build/CMakeFiles/feature_ops_lib.dir/all
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/logical_ops_tests.dir/build.make feature_operators_build/CMakeFiles/logical_ops_tests.dir/depend
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/logical_ops_tests.dir/build.make feature_operators_build/CMakeFiles/logical_ops_tests.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/git/factor_framework/build/CMakeFiles --progress-num=60,61 "Built target logical_ops_tests"
.PHONY : feature_operators_build/CMakeFiles/logical_ops_tests.dir/all

# Build rule for subdir invocation for target.
feature_operators_build/CMakeFiles/logical_ops_tests.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/factor_framework/build/CMakeFiles 13
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 feature_operators_build/CMakeFiles/logical_ops_tests.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/factor_framework/build/CMakeFiles 0
.PHONY : feature_operators_build/CMakeFiles/logical_ops_tests.dir/rule

# Convenience name for target.
logical_ops_tests: feature_operators_build/CMakeFiles/logical_ops_tests.dir/rule
.PHONY : logical_ops_tests

# clean rule for target.
feature_operators_build/CMakeFiles/logical_ops_tests.dir/clean:
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/logical_ops_tests.dir/build.make feature_operators_build/CMakeFiles/logical_ops_tests.dir/clean
.PHONY : feature_operators_build/CMakeFiles/logical_ops_tests.dir/clean

#=============================================================================
# Target rules for target feature_operators_build/CMakeFiles/comparison_ops_tests.dir

# All Build rule for target.
feature_operators_build/CMakeFiles/comparison_ops_tests.dir/all: feature_operators_build/CMakeFiles/feature_ops_lib.dir/all
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/comparison_ops_tests.dir/build.make feature_operators_build/CMakeFiles/comparison_ops_tests.dir/depend
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/comparison_ops_tests.dir/build.make feature_operators_build/CMakeFiles/comparison_ops_tests.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/git/factor_framework/build/CMakeFiles --progress-num=3,4 "Built target comparison_ops_tests"
.PHONY : feature_operators_build/CMakeFiles/comparison_ops_tests.dir/all

# Build rule for subdir invocation for target.
feature_operators_build/CMakeFiles/comparison_ops_tests.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/factor_framework/build/CMakeFiles 13
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 feature_operators_build/CMakeFiles/comparison_ops_tests.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/factor_framework/build/CMakeFiles 0
.PHONY : feature_operators_build/CMakeFiles/comparison_ops_tests.dir/rule

# Convenience name for target.
comparison_ops_tests: feature_operators_build/CMakeFiles/comparison_ops_tests.dir/rule
.PHONY : comparison_ops_tests

# clean rule for target.
feature_operators_build/CMakeFiles/comparison_ops_tests.dir/clean:
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/comparison_ops_tests.dir/build.make feature_operators_build/CMakeFiles/comparison_ops_tests.dir/clean
.PHONY : feature_operators_build/CMakeFiles/comparison_ops_tests.dir/clean

#=============================================================================
# Target rules for target feature_operators_build/CMakeFiles/reduction_ops_tests.dir

# All Build rule for target.
feature_operators_build/CMakeFiles/reduction_ops_tests.dir/all: feature_operators_build/CMakeFiles/feature_ops_lib.dir/all
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/reduction_ops_tests.dir/build.make feature_operators_build/CMakeFiles/reduction_ops_tests.dir/depend
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/reduction_ops_tests.dir/build.make feature_operators_build/CMakeFiles/reduction_ops_tests.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/git/factor_framework/build/CMakeFiles --progress-num=64,65 "Built target reduction_ops_tests"
.PHONY : feature_operators_build/CMakeFiles/reduction_ops_tests.dir/all

# Build rule for subdir invocation for target.
feature_operators_build/CMakeFiles/reduction_ops_tests.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/factor_framework/build/CMakeFiles 13
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 feature_operators_build/CMakeFiles/reduction_ops_tests.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/factor_framework/build/CMakeFiles 0
.PHONY : feature_operators_build/CMakeFiles/reduction_ops_tests.dir/rule

# Convenience name for target.
reduction_ops_tests: feature_operators_build/CMakeFiles/reduction_ops_tests.dir/rule
.PHONY : reduction_ops_tests

# clean rule for target.
feature_operators_build/CMakeFiles/reduction_ops_tests.dir/clean:
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/reduction_ops_tests.dir/build.make feature_operators_build/CMakeFiles/reduction_ops_tests.dir/clean
.PHONY : feature_operators_build/CMakeFiles/reduction_ops_tests.dir/clean

#=============================================================================
# Target rules for target feature_operators_build/CMakeFiles/panel_ops_tests.dir

# All Build rule for target.
feature_operators_build/CMakeFiles/panel_ops_tests.dir/all: feature_operators_build/CMakeFiles/feature_ops_lib.dir/all
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/panel_ops_tests.dir/build.make feature_operators_build/CMakeFiles/panel_ops_tests.dir/depend
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/panel_ops_tests.dir/build.make feature_operators_build/CMakeFiles/panel_ops_tests.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/git/factor_framework/build/CMakeFiles --progress-num=62,63 "Built target panel_ops_tests"
.PHONY : feature_operators_build/CMakeFiles/panel_ops_tests.dir/all

# Build rule for subdir invocation for target.
feature_operators_build/CMakeFiles/panel_ops_tests.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/factor_framework/build/CMakeFiles 13
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 feature_operators_build/CMakeFiles/panel_ops_tests.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/factor_framework/build/CMakeFiles 0
.PHONY : feature_operators_build/CMakeFiles/panel_ops_tests.dir/rule

# Convenience name for target.
panel_ops_tests: feature_operators_build/CMakeFiles/panel_ops_tests.dir/rule
.PHONY : panel_ops_tests

# clean rule for target.
feature_operators_build/CMakeFiles/panel_ops_tests.dir/clean:
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/panel_ops_tests.dir/build.make feature_operators_build/CMakeFiles/panel_ops_tests.dir/clean
.PHONY : feature_operators_build/CMakeFiles/panel_ops_tests.dir/clean

#=============================================================================
# Target rules for target feature_operators_build/CMakeFiles/timeseries_ops_tests.dir

# All Build rule for target.
feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/all: feature_operators_build/CMakeFiles/feature_ops_lib.dir/all
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/build.make feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/depend
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/build.make feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/git/factor_framework/build/CMakeFiles --progress-num=68,69 "Built target timeseries_ops_tests"
.PHONY : feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/all

# Build rule for subdir invocation for target.
feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/factor_framework/build/CMakeFiles 13
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/factor_framework/build/CMakeFiles 0
.PHONY : feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/rule

# Convenience name for target.
timeseries_ops_tests: feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/rule
.PHONY : timeseries_ops_tests

# clean rule for target.
feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/clean:
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/build.make feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/clean
.PHONY : feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/clean

#=============================================================================
# Target rules for target feature_operators_build/CMakeFiles/group_ops_tests.dir

# All Build rule for target.
feature_operators_build/CMakeFiles/group_ops_tests.dir/all: feature_operators_build/CMakeFiles/feature_ops_lib.dir/all
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/group_ops_tests.dir/build.make feature_operators_build/CMakeFiles/group_ops_tests.dir/depend
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/group_ops_tests.dir/build.make feature_operators_build/CMakeFiles/group_ops_tests.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/git/factor_framework/build/CMakeFiles --progress-num=58,59 "Built target group_ops_tests"
.PHONY : feature_operators_build/CMakeFiles/group_ops_tests.dir/all

# Build rule for subdir invocation for target.
feature_operators_build/CMakeFiles/group_ops_tests.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/factor_framework/build/CMakeFiles 13
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 feature_operators_build/CMakeFiles/group_ops_tests.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/factor_framework/build/CMakeFiles 0
.PHONY : feature_operators_build/CMakeFiles/group_ops_tests.dir/rule

# Convenience name for target.
group_ops_tests: feature_operators_build/CMakeFiles/group_ops_tests.dir/rule
.PHONY : group_ops_tests

# clean rule for target.
feature_operators_build/CMakeFiles/group_ops_tests.dir/clean:
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/group_ops_tests.dir/build.make feature_operators_build/CMakeFiles/group_ops_tests.dir/clean
.PHONY : feature_operators_build/CMakeFiles/group_ops_tests.dir/clean

#=============================================================================
# Target rules for target generated_factors/CMakeFiles/generated_factors.dir

# All Build rule for target.
generated_factors/CMakeFiles/generated_factors.dir/all: feature_operators_build/CMakeFiles/feature_ops_lib.dir/all
	$(MAKE) $(MAKESILENT) -f generated_factors/CMakeFiles/generated_factors.dir/build.make generated_factors/CMakeFiles/generated_factors.dir/depend
	$(MAKE) $(MAKESILENT) -f generated_factors/CMakeFiles/generated_factors.dir/build.make generated_factors/CMakeFiles/generated_factors.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/git/factor_framework/build/CMakeFiles --progress-num=26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57 "Built target generated_factors"
.PHONY : generated_factors/CMakeFiles/generated_factors.dir/all

# Build rule for subdir invocation for target.
generated_factors/CMakeFiles/generated_factors.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/factor_framework/build/CMakeFiles 43
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 generated_factors/CMakeFiles/generated_factors.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/factor_framework/build/CMakeFiles 0
.PHONY : generated_factors/CMakeFiles/generated_factors.dir/rule

# Convenience name for target.
generated_factors: generated_factors/CMakeFiles/generated_factors.dir/rule
.PHONY : generated_factors

# clean rule for target.
generated_factors/CMakeFiles/generated_factors.dir/clean:
	$(MAKE) $(MAKESILENT) -f generated_factors/CMakeFiles/generated_factors.dir/build.make generated_factors/CMakeFiles/generated_factors.dir/clean
.PHONY : generated_factors/CMakeFiles/generated_factors.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

