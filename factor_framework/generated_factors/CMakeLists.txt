# 自动生成的因子CMake文件
cmake_minimum_required(VERSION 3.10)

# 添加生成的因子源文件
set(GENERATED_FACTOR_SOURCES
    src/all_factors.cpp
    src/factorp1corrs0.cpp
    src/factorp1corrs1.cpp
    src/factorp1corrs2.cpp
    src/factorp1corrs3.cpp
    src/factorp1corrs4.cpp
    src/factorp1corrs5.cpp
    src/factorp1corrs6.cpp
    src/factorp1corrs7.cpp
    src/factorp1corrs8.cpp
    src/factorp1corrs9.cpp
    src/factorp1corrs10.cpp
    src/factorp1corrs11.cpp
    src/factorp1corrs12.cpp
    src/factorp1corrs13.cpp
    src/factorp1corrs14.cpp
    src/factorp1corrs15.cpp
    src/factorp2et0.cpp
    src/factorp2et1.cpp
    src/factorp2et2.cpp
    src/factorp2et3.cpp
    src/factorp2et4.cpp
    src/factorp2et5.cpp
    src/factorp2et6.cpp
    src/factorp2et7.cpp
    src/factorp2et8.cpp
    src/factorp2et9.cpp
    src/factorp2et10.cpp
    src/factorp2et11.cpp
    src/factorp2et12.cpp
    src/factorp2et13.cpp
    src/factorp2et14.cpp
    src/factorp2et15.cpp
    src/factorp2et16.cpp
    src/factorp2et17.cpp
    src/factorp2et18.cpp
    src/factorp2et19.cpp
    src/factorp3mf0.cpp
    src/factorp3mf1.cpp
    src/factorp3mf2.cpp
    src/factorp3mf3.cpp
    src/factorp3mf4.cpp
    src/factorp3mf5.cpp
    src/factorp3mf6.cpp
    src/factorp3mf7.cpp
    src/factorp3mf8.cpp
    src/factorp3mf9.cpp
    src/factorp3mf10.cpp
    src/factorp3mf11.cpp
    src/factorp3mf12.cpp
    src/factorp4ms0.cpp
    src/factorp4ms1.cpp
    src/factorp4ms2.cpp
    src/factorp4ms3.cpp
    src/factorp4ms4.cpp
    src/factorp4ms5.cpp
    src/factorp4ms6.cpp
    src/factorp5to0.cpp
    src/factorp5to1.cpp
    src/factorp5to2.cpp
    src/factorp5to3.cpp
    src/factorp5to4.cpp
    src/factorp5to5.cpp
    src/factorp5to6.cpp
    src/factorp5to7.cpp
    src/factorp6tn0.cpp
    src/factorp6tn1.cpp
    src/factorp6tn2.cpp
    src/factorp6tn3.cpp
    src/factorp6tn4.cpp
    src/factorp6tn5.cpp
    src/factorp6tn6.cpp
    src/factorp6tn7.cpp
    src/factorp6tn8.cpp
    src/factorp6tn9.cpp
    src/factorp6tn10.cpp
    src/factorp6tn11.cpp
    src/factorp6tn12.cpp
    src/factorp6tn13.cpp
)

# 添加生成的因子头文件目录
set(GENERATED_FACTOR_INCLUDE_DIRS
    include
)

# 创建生成因子库
add_library(generated_factors STATIC ${GENERATED_FACTOR_SOURCES})

# 设置包含目录
target_include_directories(generated_factors PUBLIC
    ${GENERATED_FACTOR_INCLUDE_DIRS}
    ${CMAKE_SOURCE_DIR}/include
)

# 链接feature_operators库
target_link_libraries(generated_factors
    feature_ops_lib
)

# 设置编译选项
target_compile_features(generated_factors PUBLIC cxx_std_17)
target_compile_options(generated_factors PRIVATE
    $<$<CXX_COMPILER_ID:GNU>:-Wall -Wextra -O3>
    $<$<CXX_COMPILER_ID:Clang>:-Wall -Wextra -O3>
    $<$<CXX_COMPILER_ID:MSVC>:/W4 /O2>
)

# 安装规则
install(TARGETS generated_factors
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
)

install(DIRECTORY include/
    DESTINATION include
    FILES_MATCHING PATTERN "*.hpp"
)