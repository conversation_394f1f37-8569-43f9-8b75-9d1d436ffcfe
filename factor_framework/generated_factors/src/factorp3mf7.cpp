#include "generated_factors/factorp3mf7.hpp"
#include <stdexcept>

namespace generated_factors {

FactorP3mf7::FactorP3mf7(int factor_id, const std::string& factor_name, const std::string& formula)
    : factor_base(factor_id, factor_name, formula) {
}

feature_operators::DataFrame FactorP3mf7::calculate(
    const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) {

    // 验证输入数据
    if (!validate_input(data_map)) {
        throw std::runtime_error("Invalid input data for factor " + get_name());
    }

    try {
        // 优化信息：提取了 0 个公共子表达式
        // 执行因子计算
        auto result = feature_operators::Tot_Mean(feature_operators::ts_Stdev(((get_field(data_map, "Close")/feature_operators::ts_Delay(get_field(data_map, "Close"),1))-1),10));
        return result;
    } catch (const std::exception& e) {
        throw std::runtime_error("Error calculating factor " + get_name() + ": " + e.what());
    }
}

std::vector<std::string> FactorP3mf7::get_required_fields() const {
    return {"Close"};
}

// 简化的因子注册 - 注册到静态列表

} // namespace generated_factors