#include "generated_factors/all_factors.hpp"
#include <iostream>
#include <algorithm>

namespace generated_factors {

std::vector<std::string> get_all_generated_factor_names() {
    return {"p1_corrs0", "p1_corrs1", "p1_corrs2", "p1_corrs3", "p1_corrs4", "p1_corrs5", "p1_corrs6", "p1_corrs7", "p1_corrs8", "p1_corrs9", "p1_corrs10", "p1_corrs11", "p1_corrs12", "p1_corrs13", "p1_corrs14", "p1_corrs15", "p2_et0", "p2_et1", "p2_et2", "p2_et3", "p2_et4", "p2_et5", "p2_et6", "p2_et7", "p2_et8", "p2_et9", "p2_et10", "p2_et11", "p2_et12", "p2_et13", "p2_et14", "p2_et15", "p2_et16", "p2_et17", "p2_et18", "p2_et19", "p3_mf0", "p3_mf1", "p3_mf2", "p3_mf3", "p3_mf4", "p3_mf5", "p3_mf6", "p3_mf7", "p3_mf8", "p3_mf9", "p3_mf10", "p3_mf11", "p3_mf12", "p4_ms0", "p4_ms1", "p4_ms2", "p4_ms3", "p4_ms4", "p4_ms5", "p4_ms6", "p5_to0", "p5_to1", "p5_to2", "p5_to3", "p5_to4", "p5_to5", "p5_to6", "p5_to7", "p6_tn0", "p6_tn1", "p6_tn2", "p6_tn3", "p6_tn4", "p6_tn5", "p6_tn6", "p6_tn7", "p6_tn8", "p6_tn9", "p6_tn10", "p6_tn11", "p6_tn12", "p6_tn13"};
}

std::vector<int> get_all_generated_factor_ids() {
    return {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77};
}

void initialize_all_factors() {
    // 这个函数确保所有因子类被链接器包含
    static bool initialized = false;
    if (!initialized) {
        std::cout << "✓ Generated factors library initialized" << std::endl;
        std::cout << "✓ Available factors: " << 78 << std::endl;

        // 显示所有可用的因子
        auto factor_names = get_all_generated_factor_names();
        for (const auto& factor_name : factor_names) {
            std::cout << "  - " << factor_name << std::endl;
        }

        initialized = true;
    }
}

/**
 * 批量注册所有生成的因子到factor_manager
 * @param manager 因子管理器
 * @return 成功注册的因子数量
 */
int register_all_factors_to_manager(std::shared_ptr<factor_framework::factor_manager> manager) {
    if (!manager) {
        std::cerr << "Error: factor_manager is null" << std::endl;
        return 0;
    }

    // 从静态注册列表创建所有因子实例
    auto& registration_list = factor_framework::get_factor_registration_list();

    int registered_count = 0;
    for (auto& creator : registration_list) {
        try {
            auto factor = creator();
            if (factor && manager->register_factor(std::move(factor))) {
                registered_count++;
            }
        } catch (const std::exception& e) {
            std::cerr << "Error creating factor: " << e.what() << std::endl;
        }
    }

    std::cout << "✓ Registered " << registered_count << " generated factors to manager" << std::endl;
    return registered_count;
}

} // namespace generated_factors