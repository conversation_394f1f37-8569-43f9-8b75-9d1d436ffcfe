#include "generated_factors/factorp1corrs14.hpp"
#include <stdexcept>

namespace generated_factors {

FactorP1corrs14::FactorP1corrs14(int factor_id, const std::string& factor_name, const std::string& formula)
    : factor_base(factor_id, factor_name, formula) {
}

feature_operators::DataFrame FactorP1corrs14::calculate(
    const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) {

    // 验证输入数据
    if (!validate_input(data_map)) {
        throw std::runtime_error("Invalid input data for factor " + get_name());
    }

    try {
        // 优化信息：提取了 0 个公共子表达式
        // 执行因子计算
        auto result = feature_operators::ts_Corr(get_field(data_map, "Volume"),(get_field(data_map, "Volume")-feature_operators::ts_Delay(get_field(data_map, "Volume"),1)),60);
        return result;
    } catch (const std::exception& e) {
        throw std::runtime_error("Error calculating factor " + get_name() + ": " + e.what());
    }
}

std::vector<std::string> FactorP1corrs14::get_required_fields() const {
    return {"Volume"};
}

// 简化的因子注册 - 注册到静态列表

} // namespace generated_factors