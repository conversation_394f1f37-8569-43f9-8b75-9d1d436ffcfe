#ifndef GENERATED_FACTORS_FACTORP6TN10_HPP
#define GENERATED_FACTORS_FACTORP6TN10_HPP

#include "factor_framework/factor_base.hpp"
#include "feature_operators.hpp"

namespace generated_factors {

/**
 * p6_tn10 因子
 * ID: 74
 * 公式: Tot_Mean(IfThen(Tot_Rank(0.01-(Close-ts_Delay(Close,1))/Close),(Close-ts_Delay(Close,1))/Close,getNan(Close)))
 */
class FactorP6tn10 : public factor_framework::factor_base {
public:
    /**
     * 构造函数
     */
    FactorP6tn10(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 计算因子值
     */
    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override;

    /**
     * 获取所需数据字段
     */
    std::vector<std::string> get_required_fields() const override;
};
REGISTER_FACTOR_SIMPLE(FactorP6tn10, 74, "p6_tn10", "Tot_Mean(IfThen(Tot_Rank(0.01-(Close-ts_Delay(Close,1))/Close),(Close-ts_Delay(Close,1))/Close,getNan(Close)))")

} // namespace generated_factors

#endif // GENERATED_FACTORS_FACTORP6TN10_HPP