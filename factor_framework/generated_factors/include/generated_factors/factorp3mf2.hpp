#ifndef GENERATED_FACTORS_FACTORP3MF2_HPP
#define GENERATED_FACTORS_FACTORP3MF2_HPP

#include "factor_framework/factor_base.hpp"
#include "feature_operators.hpp"

namespace generated_factors {

/**
 * p3_mf2 因子
 * ID: 38
 * 公式: Tot_Mean(IfThen(Tot_Rank(Abs(Close/Open-1)/Sqrt(Volume))-0.8,Close,getNan(Close)))
 */
class FactorP3mf2 : public factor_framework::factor_base {
public:
    /**
     * 构造函数
     */
    FactorP3mf2(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 计算因子值
     */
    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override;

    /**
     * 获取所需数据字段
     */
    std::vector<std::string> get_required_fields() const override;
};
REGISTER_FACTOR_SIMPLE(FactorP3mf2, 38, "p3_mf2", "Tot_Mean(IfThen(Tot_Rank(Abs(Close/Open-1)/Sqrt(Volume))-0.8,Close,getNan(Close)))")

} // namespace generated_factors

#endif // GENERATED_FACTORS_FACTORP3MF2_HPP