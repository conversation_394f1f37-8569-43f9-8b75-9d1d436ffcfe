#ifndef GENERATED_FACTORS_FACTORP5TO5_HPP
#define GENERATED_FACTORS_FACTORP5TO5_HPP

#include "factor_framework/factor_base.hpp"
#include "feature_operators.hpp"

namespace generated_factors {

/**
 * p5_to5 因子
 * ID: 61
 * 公式: Tot_Sum((High-Low)/Close)/Tot_Sum(Amount)
 */
class FactorP5to5 : public factor_framework::factor_base {
public:
    /**
     * 构造函数
     */
    FactorP5to5(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 计算因子值
     */
    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override;

    /**
     * 获取所需数据字段
     */
    std::vector<std::string> get_required_fields() const override;
};
REGISTER_FACTOR_SIMPLE(FactorP5to5, 61, "p5_to5", "Tot_Sum((High-Low)/Close)/Tot_Sum(Amount)")

} // namespace generated_factors

#endif // GENERATED_FACTORS_FACTORP5TO5_HPP