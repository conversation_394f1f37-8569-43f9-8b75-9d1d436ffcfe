#ifndef GENERATED_FACTORS_FACTORP6TN8_HPP
#define GENERATED_FACTORS_FACTORP6TN8_HPP

#include "factor_framework/factor_base.hpp"
#include "feature_operators.hpp"

namespace generated_factors {

/**
 * p6_tn8 因子
 * ID: 72
 * 公式: Tot_Mean((High-Low)/Close)
 */
class FactorP6tn8 : public factor_framework::factor_base {
public:
    /**
     * 构造函数
     */
    FactorP6tn8(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 计算因子值
     */
    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override;

    /**
     * 获取所需数据字段
     */
    std::vector<std::string> get_required_fields() const override;
};
REGISTER_FACTOR_SIMPLE(FactorP6tn8, 72, "p6_tn8", "Tot_Mean((High-Low)/Close)")

} // namespace generated_factors

#endif // GENERATED_FACTORS_FACTORP6TN8_HPP