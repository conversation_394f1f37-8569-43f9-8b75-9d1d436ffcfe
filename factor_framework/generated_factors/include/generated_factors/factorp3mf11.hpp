#ifndef GENERATED_FACTORS_FACTORP3MF11_HPP
#define GENERATED_FACTORS_FACTORP3MF11_HPP

#include "factor_framework/factor_base.hpp"
#include "feature_operators.hpp"

namespace generated_factors {

/**
 * p3_mf11 因子
 * ID: 47
 * 公式: Tot_Sum(IfThen(Tot_Rank(Volume)-0.8,(Close-Open)/Open,getNan(Close)))
 */
class FactorP3mf11 : public factor_framework::factor_base {
public:
    /**
     * 构造函数
     */
    FactorP3mf11(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 计算因子值
     */
    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override;

    /**
     * 获取所需数据字段
     */
    std::vector<std::string> get_required_fields() const override;
};
REGISTER_FACTOR_SIMPLE(FactorP3mf11, 47, "p3_mf11", "Tot_Sum(IfThen(Tot_Rank(Volume)-0.8,(Close-Open)/Open,getNan(Close)))")

} // namespace generated_factors

#endif // GENERATED_FACTORS_FACTORP3MF11_HPP