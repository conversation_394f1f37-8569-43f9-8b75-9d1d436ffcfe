#ifndef GENERATED_FACTORS_FACTORP3MF10_HPP
#define GENERATED_FACTORS_FACTORP3MF10_HPP

#include "factor_framework/factor_base.hpp"
#include "feature_operators.hpp"

namespace generated_factors {

/**
 * p3_mf10 因子
 * ID: 46
 * 公式: Tot_Sum(IfThen(ts_Stdev(ts_Stdev(Close/ts_Delay(Close,1)-1,10),10)-Tot_Mean(ts_Stdev(ts_Stdev(Close/ts_Delay(Close,1)-1,10),10)),Volume*Close,getNan(Close)))
 */
class FactorP3mf10 : public factor_framework::factor_base {
public:
    /**
     * 构造函数
     */
    FactorP3mf10(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 计算因子值
     */
    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override;

    /**
     * 获取所需数据字段
     */
    std::vector<std::string> get_required_fields() const override;
};
REGISTER_FACTOR_SIMPLE(FactorP3mf10, 46, "p3_mf10", "Tot_Sum(IfThen(ts_Stdev(ts_Stdev(Close/ts_Delay(Close,1)-1,10),10)-Tot_Mean(ts_Stdev(ts_Stdev(Close/ts_Delay(Close,1)-1,10),10)),Volume*Close,getNan(Close)))")

} // namespace generated_factors

#endif // GENERATED_FACTORS_FACTORP3MF10_HPP