#ifndef GENERATED_FACTORS_FACTORP2ET11_HPP
#define GENERATED_FACTORS_FACTORP2ET11_HPP

#include "factor_framework/factor_base.hpp"
#include "feature_operators.hpp"

namespace generated_factors {

/**
 * p2_et11 因子
 * ID: 27
 * 公式: Tot_Sum(IfThen(ts_Delay(ts_Min(Low,30),1)-Low,1,0))
 */
class FactorP2et11 : public factor_framework::factor_base {
public:
    /**
     * 构造函数
     */
    FactorP2et11(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 计算因子值
     */
    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override;

    /**
     * 获取所需数据字段
     */
    std::vector<std::string> get_required_fields() const override;
};
REGISTER_FACTOR_SIMPLE(FactorP2et11, 27, "p2_et11", "Tot_Sum(IfThen(ts_Delay(ts_Min(Low,30),1)-Low,1,0))")

} // namespace generated_factors

#endif // GENERATED_FACTORS_FACTORP2ET11_HPP