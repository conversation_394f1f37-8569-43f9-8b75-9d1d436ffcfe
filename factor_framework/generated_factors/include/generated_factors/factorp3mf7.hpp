#ifndef GENERATED_FACTORS_FACTORP3MF7_HPP
#define GENERATED_FACTORS_FACTORP3MF7_HPP

#include "factor_framework/factor_base.hpp"
#include "feature_operators.hpp"

namespace generated_factors {

/**
 * p3_mf7 因子
 * ID: 43
 * 公式: Tot_Mean(ts_Stdev(Close/ts_Delay(Close,1)-1,10))
 */
class FactorP3mf7 : public factor_framework::factor_base {
public:
    /**
     * 构造函数
     */
    FactorP3mf7(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 计算因子值
     */
    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override;

    /**
     * 获取所需数据字段
     */
    std::vector<std::string> get_required_fields() const override;
};
REGISTER_FACTOR_SIMPLE(FactorP3mf7, 43, "p3_mf7", "Tot_Mean(ts_Stdev(Close/ts_Delay(Close,1)-1,10))")

} // namespace generated_factors

#endif // GENERATED_FACTORS_FACTORP3MF7_HPP