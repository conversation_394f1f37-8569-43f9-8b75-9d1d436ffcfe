#ifndef GENERATED_FACTORS_FACTORP2ET9_HPP
#define GENERATED_FACTORS_FACTORP2ET9_HPP

#include "factor_framework/factor_base.hpp"
#include "feature_operators.hpp"

namespace generated_factors {

/**
 * p2_et9 因子
 * ID: 25
 * 公式: ts_Mean((Close/ts_Delay(Close,1)-1)*IfThen(Volume-ts_Mean(Volume,30)-1.210*ts_Stdev(Volume,30),1,0),30)
 */
class FactorP2et9 : public factor_framework::factor_base {
public:
    /**
     * 构造函数
     */
    FactorP2et9(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 计算因子值
     */
    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override;

    /**
     * 获取所需数据字段
     */
    std::vector<std::string> get_required_fields() const override;
};
REGISTER_FACTOR_SIMPLE(FactorP2et9, 25, "p2_et9", "ts_Mean((Close/ts_Delay(Close,1)-1)*IfThen(Volume-ts_Mean(Volume,30)-1.210*ts_Stdev(Volume,30),1,0),30)")

} // namespace generated_factors

#endif // GENERATED_FACTORS_FACTORP2ET9_HPP