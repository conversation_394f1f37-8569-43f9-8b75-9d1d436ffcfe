#ifndef GENERATED_FACTORS_FACTORP2ET10_HPP
#define GENERATED_FACTORS_FACTORP2ET10_HPP

#include "factor_framework/factor_base.hpp"
#include "feature_operators.hpp"

namespace generated_factors {

/**
 * p2_et10 因子
 * ID: 26
 * 公式: ts_Mean(IfThen((Close/ts_Delay(Close,1)-1),(Close/ts_Delay(Close,1)-1),0)*IfThen(Volume-ts_Mean(Volume,30)-0.10*ts_Stdev(Volume,30),1,0),30)
 */
class FactorP2et10 : public factor_framework::factor_base {
public:
    /**
     * 构造函数
     */
    FactorP2et10(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 计算因子值
     */
    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override;

    /**
     * 获取所需数据字段
     */
    std::vector<std::string> get_required_fields() const override;
};
REGISTER_FACTOR_SIMPLE(FactorP2et10, 26, "p2_et10", "ts_Mean(IfThen((Close/ts_Delay(Close,1)-1),(Close/ts_Delay(Close,1)-1),0)*IfThen(Volume-ts_Mean(Volume,30)-0.10*ts_Stdev(Volume,30),1,0),30)")

} // namespace generated_factors

#endif // GENERATED_FACTORS_FACTORP2ET10_HPP