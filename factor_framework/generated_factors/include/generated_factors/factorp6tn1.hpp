#ifndef GENERATED_FACTORS_FACTORP6TN1_HPP
#define GENERATED_FACTORS_FACTORP6TN1_HPP

#include "factor_framework/factor_base.hpp"
#include "feature_operators.hpp"

namespace generated_factors {

/**
 * p6_tn1 因子
 * ID: 65
 * 公式: Tot_Sum(High-Open)-Tot_Sum(Open-Low)
 */
class FactorP6tn1 : public factor_framework::factor_base {
public:
    /**
     * 构造函数
     */
    FactorP6tn1(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 计算因子值
     */
    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override;

    /**
     * 获取所需数据字段
     */
    std::vector<std::string> get_required_fields() const override;
};
REGISTER_FACTOR_SIMPLE(FactorP6tn1, 65, "p6_tn1", "Tot_Sum(High-Open)-Tot_Sum(Open-Low)")

} // namespace generated_factors

#endif // GENERATED_FACTORS_FACTORP6TN1_HPP