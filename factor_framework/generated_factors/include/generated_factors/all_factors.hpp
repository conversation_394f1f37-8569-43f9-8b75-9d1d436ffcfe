#ifndef GENERATED_FACTORS_ALL_FACTORS_HPP
#define GENERATED_FACTORS_ALL_FACTORS_HPP

/**
 * 自动生成的因子头文件
 * 包含所有从feature.csv生成的因子类
 *
 * 生成的因子数量: 78
 */

// 包含因子框架
#include "factor_framework/factor_framework.hpp"

// 包含所有生成的因子
#include "generated_factors/factorp1corrs0.hpp"
#include "generated_factors/factorp1corrs1.hpp"
#include "generated_factors/factorp1corrs2.hpp"
#include "generated_factors/factorp1corrs3.hpp"
#include "generated_factors/factorp1corrs4.hpp"
#include "generated_factors/factorp1corrs5.hpp"
#include "generated_factors/factorp1corrs6.hpp"
#include "generated_factors/factorp1corrs7.hpp"
#include "generated_factors/factorp1corrs8.hpp"
#include "generated_factors/factorp1corrs9.hpp"
#include "generated_factors/factorp1corrs10.hpp"
#include "generated_factors/factorp1corrs11.hpp"
#include "generated_factors/factorp1corrs12.hpp"
#include "generated_factors/factorp1corrs13.hpp"
#include "generated_factors/factorp1corrs14.hpp"
#include "generated_factors/factorp1corrs15.hpp"
#include "generated_factors/factorp2et0.hpp"
#include "generated_factors/factorp2et1.hpp"
#include "generated_factors/factorp2et2.hpp"
#include "generated_factors/factorp2et3.hpp"
#include "generated_factors/factorp2et4.hpp"
#include "generated_factors/factorp2et5.hpp"
#include "generated_factors/factorp2et6.hpp"
#include "generated_factors/factorp2et7.hpp"
#include "generated_factors/factorp2et8.hpp"
#include "generated_factors/factorp2et9.hpp"
#include "generated_factors/factorp2et10.hpp"
#include "generated_factors/factorp2et11.hpp"
#include "generated_factors/factorp2et12.hpp"
#include "generated_factors/factorp2et13.hpp"
#include "generated_factors/factorp2et14.hpp"
#include "generated_factors/factorp2et15.hpp"
#include "generated_factors/factorp2et16.hpp"
#include "generated_factors/factorp2et17.hpp"
#include "generated_factors/factorp2et18.hpp"
#include "generated_factors/factorp2et19.hpp"
#include "generated_factors/factorp3mf0.hpp"
#include "generated_factors/factorp3mf1.hpp"
#include "generated_factors/factorp3mf2.hpp"
#include "generated_factors/factorp3mf3.hpp"
#include "generated_factors/factorp3mf4.hpp"
#include "generated_factors/factorp3mf5.hpp"
#include "generated_factors/factorp3mf6.hpp"
#include "generated_factors/factorp3mf7.hpp"
#include "generated_factors/factorp3mf8.hpp"
#include "generated_factors/factorp3mf9.hpp"
#include "generated_factors/factorp3mf10.hpp"
#include "generated_factors/factorp3mf11.hpp"
#include "generated_factors/factorp3mf12.hpp"
#include "generated_factors/factorp4ms0.hpp"
#include "generated_factors/factorp4ms1.hpp"
#include "generated_factors/factorp4ms2.hpp"
#include "generated_factors/factorp4ms3.hpp"
#include "generated_factors/factorp4ms4.hpp"
#include "generated_factors/factorp4ms5.hpp"
#include "generated_factors/factorp4ms6.hpp"
#include "generated_factors/factorp5to0.hpp"
#include "generated_factors/factorp5to1.hpp"
#include "generated_factors/factorp5to2.hpp"
#include "generated_factors/factorp5to3.hpp"
#include "generated_factors/factorp5to4.hpp"
#include "generated_factors/factorp5to5.hpp"
#include "generated_factors/factorp5to6.hpp"
#include "generated_factors/factorp5to7.hpp"
#include "generated_factors/factorp6tn0.hpp"
#include "generated_factors/factorp6tn1.hpp"
#include "generated_factors/factorp6tn2.hpp"
#include "generated_factors/factorp6tn3.hpp"
#include "generated_factors/factorp6tn4.hpp"
#include "generated_factors/factorp6tn5.hpp"
#include "generated_factors/factorp6tn6.hpp"
#include "generated_factors/factorp6tn7.hpp"
#include "generated_factors/factorp6tn8.hpp"
#include "generated_factors/factorp6tn9.hpp"
#include "generated_factors/factorp6tn10.hpp"
#include "generated_factors/factorp6tn11.hpp"
#include "generated_factors/factorp6tn12.hpp"
#include "generated_factors/factorp6tn13.hpp"

namespace generated_factors {

/**
 * 获取所有生成的因子名称
 */
std::vector<std::string> get_all_generated_factor_names();

/**
 * 获取所有生成的因子ID
 */
std::vector<int> get_all_generated_factor_ids();

/**
 * 初始化所有生成的因子（确保注册）
 */
void initialize_all_factors();

/**
 * 批量注册所有生成的因子到factor_manager
 * @param manager 因子管理器
 * @return 成功注册的因子数量
 */
int register_all_factors_to_manager(std::shared_ptr<factor_framework::factor_manager> manager);

} // namespace generated_factors

#endif // GENERATED_FACTORS_ALL_FACTORS_HPP