#ifndef GENERATED_FACTORS_FACTORP6TN6_HPP
#define GENERATED_FACTORS_FACTORP6TN6_HPP

#include "factor_framework/factor_base.hpp"
#include "feature_operators.hpp"

namespace generated_factors {

/**
 * p6_tn6 因子
 * ID: 70
 * 公式: Tot_Mean((ts_Max(High,30)-Close)/(ts_Max(High,30)-ts_Min(Low,30)))
 */
class FactorP6tn6 : public factor_framework::factor_base {
public:
    /**
     * 构造函数
     */
    FactorP6tn6(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 计算因子值
     */
    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override;

    /**
     * 获取所需数据字段
     */
    std::vector<std::string> get_required_fields() const override;
};
REGISTER_FACTOR_SIMPLE(FactorP6tn6, 70, "p6_tn6", "Tot_Mean((ts_Max(High,30)-Close)/(ts_Max(High,30)-ts_Min(Low,30)))")

} // namespace generated_factors

#endif // GENERATED_FACTORS_FACTORP6TN6_HPP