#ifndef GENERATED_FACTORS_FACTORP4MS4_HPP
#define GENERATED_FACTORS_FACTORP4MS4_HPP

#include "factor_framework/factor_base.hpp"
#include "feature_operators.hpp"

namespace generated_factors {

/**
 * p4_ms4 因子
 * ID: 53
 * 公式: Tot_Sum(Power((Close-ts_Delay(Close,1))/Close,2))
 */
class FactorP4ms4 : public factor_framework::factor_base {
public:
    /**
     * 构造函数
     */
    FactorP4ms4(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 计算因子值
     */
    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override;

    /**
     * 获取所需数据字段
     */
    std::vector<std::string> get_required_fields() const override;
};
REGISTER_FACTOR_SIMPLE(FactorP4ms4, 53, "p4_ms4", "Tot_Sum(Power((Close-ts_Delay(Close,1))/Close,2))")

} // namespace generated_factors

#endif // GENERATED_FACTORS_FACTORP4MS4_HPP