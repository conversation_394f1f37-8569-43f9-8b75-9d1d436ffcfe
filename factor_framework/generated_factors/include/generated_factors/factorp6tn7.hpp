#ifndef GENERATED_FACTORS_FACTORP6TN7_HPP
#define GENERATED_FACTORS_FACTORP6TN7_HPP

#include "factor_framework/factor_base.hpp"
#include "feature_operators.hpp"

namespace generated_factors {

/**
 * p6_tn7 因子
 * ID: 71
 * 公式: Tot_Mean((Close-ts_Min(Low,30))/(ts_Max(High,30)-ts_Min(Low,30)))
 */
class FactorP6tn7 : public factor_framework::factor_base {
public:
    /**
     * 构造函数
     */
    FactorP6tn7(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 计算因子值
     */
    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override;

    /**
     * 获取所需数据字段
     */
    std::vector<std::string> get_required_fields() const override;
};
REGISTER_FACTOR_SIMPLE(FactorP6tn7, 71, "p6_tn7", "Tot_Mean((Close-ts_Min(Low,30))/(ts_Max(High,30)-ts_Min(Low,30)))")

} // namespace generated_factors

#endif // GENERATED_FACTORS_FACTORP6TN7_HPP