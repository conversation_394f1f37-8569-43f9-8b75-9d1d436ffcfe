#ifndef GENERATED_FACTORS_FACTORP5TO0_HPP
#define GENERATED_FACTORS_FACTORP5TO0_HPP

#include "factor_framework/factor_base.hpp"
#include "feature_operators.hpp"

namespace generated_factors {

/**
 * p5_to0 因子
 * ID: 56
 * 公式: Tot_Sum(IfThen(Close-ts_Delay(Close,1),Amount,-Amount))
 */
class FactorP5to0 : public factor_framework::factor_base {
public:
    /**
     * 构造函数
     */
    FactorP5to0(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 计算因子值
     */
    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override;

    /**
     * 获取所需数据字段
     */
    std::vector<std::string> get_required_fields() const override;
};
REGISTER_FACTOR_SIMPLE(FactorP5to0, 56, "p5_to0", "Tot_Sum(IfThen(Close-ts_Delay(Close,1),Amount,-Amount))")

} // namespace generated_factors

#endif // GENERATED_FACTORS_FACTORP5TO0_HPP