#ifndef GENERATED_FACTORS_FACTORP6TN12_HPP
#define GENERATED_FACTORS_FACTORP6TN12_HPP

#include "factor_framework/factor_base.hpp"
#include "feature_operators.hpp"

namespace generated_factors {

/**
 * p6_tn12 因子
 * ID: 76
 * 公式: Tot_Sum(IfThen(Tot_Rank(High-Low)-0.10,Volume,0))-Tot_Sum(IfThen(Tot_Rank(High-Low)-0.10,0,Volume))
 */
class FactorP6tn12 : public factor_framework::factor_base {
public:
    /**
     * 构造函数
     */
    FactorP6tn12(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 计算因子值
     */
    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override;

    /**
     * 获取所需数据字段
     */
    std::vector<std::string> get_required_fields() const override;
};
REGISTER_FACTOR_SIMPLE(FactorP6tn12, 76, "p6_tn12", "Tot_Sum(IfThen(Tot_Rank(High-Low)-0.10,Volume,0))-Tot_Sum(IfThen(Tot_Rank(High-Low)-0.10,0,Volume))")

} // namespace generated_factors

#endif // GENERATED_FACTORS_FACTORP6TN12_HPP