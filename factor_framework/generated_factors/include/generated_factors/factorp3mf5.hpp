#ifndef GENERATED_FACTORS_FACTORP3MF5_HPP
#define GENERATED_FACTORS_FACTORP3MF5_HPP

#include "factor_framework/factor_base.hpp"
#include "feature_operators.hpp"

namespace generated_factors {

/**
 * p3_mf5 因子
 * ID: 41
 * 公式: Tot_Sum(IfThen(Equal(Abs(Close-Open),Abs(High-Low)),Amount,getNan(Close)))
 */
class FactorP3mf5 : public factor_framework::factor_base {
public:
    /**
     * 构造函数
     */
    FactorP3mf5(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 计算因子值
     */
    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override;

    /**
     * 获取所需数据字段
     */
    std::vector<std::string> get_required_fields() const override;
};
REGISTER_FACTOR_SIMPLE(FactorP3mf5, 41, "p3_mf5", "Tot_Sum(IfThen(Equal(Abs(Close-Open),Abs(High-Low)),Amount,getNan(Close)))")

} // namespace generated_factors

#endif // GENERATED_FACTORS_FACTORP3MF5_HPP