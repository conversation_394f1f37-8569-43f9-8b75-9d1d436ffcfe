#ifndef GENERATED_FACTORS_FACTORP2ET19_HPP
#define GENERATED_FACTORS_FACTORP2ET19_HPP

#include "factor_framework/factor_base.hpp"
#include "feature_operators.hpp"

namespace generated_factors {

/**
 * p2_et19 因子
 * ID: 35
 * 公式: Tot_Sum(IfThen(Equal(Close/ts_Delay(Close,1)-1,0),1,get<PERSON>an(Close)))
 */
class FactorP2et19 : public factor_framework::factor_base {
public:
    /**
     * 构造函数
     */
    FactorP2et19(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 计算因子值
     */
    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override;

    /**
     * 获取所需数据字段
     */
    std::vector<std::string> get_required_fields() const override;
};
REGISTER_FACTOR_SIMPLE(FactorP2et19, 35, "p2_et19", "Tot_Sum(IfThen(Equal(Close/ts_Delay(Close,1)-1,0),1,getNan(Close)))")

} // namespace generated_factors

#endif // GENERATED_FACTORS_FACTORP2ET19_HPP