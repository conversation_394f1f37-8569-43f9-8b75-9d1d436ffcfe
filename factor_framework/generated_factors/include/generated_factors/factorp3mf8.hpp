#ifndef GENERATED_FACTORS_FACTORP3MF8_HPP
#define GENERATED_FACTORS_FACTORP3MF8_HPP

#include "factor_framework/factor_base.hpp"
#include "feature_operators.hpp"

namespace generated_factors {

/**
 * p3_mf8 因子
 * ID: 44
 * 公式: Tot_Mean(ts_Stdev(ts_Stdev(Close/ts_Delay(Close,1)-1,10),10))
 */
class FactorP3mf8 : public factor_framework::factor_base {
public:
    /**
     * 构造函数
     */
    FactorP3mf8(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 计算因子值
     */
    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override;

    /**
     * 获取所需数据字段
     */
    std::vector<std::string> get_required_fields() const override;
};
REGISTER_FACTOR_SIMPLE(FactorP3mf8, 44, "p3_mf8", "Tot_Mean(ts_Stdev(ts_Stdev(Close/ts_Delay(Close,1)-1,10),10))")

} // namespace generated_factors

#endif // GENERATED_FACTORS_FACTORP3MF8_HPP