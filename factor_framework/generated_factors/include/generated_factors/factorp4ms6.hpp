#ifndef GENERATED_FACTORS_FACTORP4MS6_HPP
#define GENERATED_FACTORS_FACTORP4MS6_HPP

#include "factor_framework/factor_base.hpp"
#include "feature_operators.hpp"

namespace generated_factors {

/**
 * p4_ms6 因子
 * ID: 55
 * 公式: Tot_Sum(IfThen(Close/ts_Delay(Close,1)-1,Volume,getNan(Close)))
 */
class FactorP4ms6 : public factor_framework::factor_base {
public:
    /**
     * 构造函数
     */
    FactorP4ms6(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 计算因子值
     */
    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override;

    /**
     * 获取所需数据字段
     */
    std::vector<std::string> get_required_fields() const override;
};
REGISTER_FACTOR_SIMPLE(FactorP4ms6, 55, "p4_ms6", "Tot_Sum(IfThen(Close/ts_Delay(Close,1)-1,Volume,getNan(Close)))")

} // namespace generated_factors

#endif // GENERATED_FACTORS_FACTORP4MS6_HPP