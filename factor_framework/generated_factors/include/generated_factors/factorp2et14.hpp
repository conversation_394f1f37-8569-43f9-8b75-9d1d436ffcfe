#ifndef GENERATED_FACTORS_FACTORP2ET14_HPP
#define GENERATED_FACTORS_FACTORP2ET14_HPP

#include "factor_framework/factor_base.hpp"
#include "feature_operators.hpp"

namespace generated_factors {

/**
 * p2_et14 因子
 * ID: 30
 * 公式: Tot_Stdev(pn_Rank(Volume))
 */
class FactorP2et14 : public factor_framework::factor_base {
public:
    /**
     * 构造函数
     */
    FactorP2et14(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 计算因子值
     */
    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override;

    /**
     * 获取所需数据字段
     */
    std::vector<std::string> get_required_fields() const override;
};
REGISTER_FACTOR_SIMPLE(FactorP2et14, 30, "p2_et14", "Tot_Stdev(pn_Rank(Volume))")

} // namespace generated_factors

#endif // GENERATED_FACTORS_FACTORP2ET14_HPP