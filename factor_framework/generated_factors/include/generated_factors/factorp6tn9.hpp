#ifndef GENERATED_FACTORS_FACTORP6TN9_HPP
#define GENERATED_FACTORS_FACTORP6TN9_HPP

#include "factor_framework/factor_base.hpp"
#include "feature_operators.hpp"

namespace generated_factors {

/**
 * p6_tn9 因子
 * ID: 73
 * 公式: Tot_Mean(IfThen(Tot_Rank((Close-ts_Delay(Close,1))/Close)-0.910,(Close-ts_Delay(Close,1))/Close,getNan(Close)))
 */
class FactorP6tn9 : public factor_framework::factor_base {
public:
    /**
     * 构造函数
     */
    FactorP6tn9(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 计算因子值
     */
    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override;

    /**
     * 获取所需数据字段
     */
    std::vector<std::string> get_required_fields() const override;
};
REGISTER_FACTOR_SIMPLE(FactorP6tn9, 73, "p6_tn9", "Tot_Mean(IfThen(Tot_Rank((Close-ts_Delay(Close,1))/Close)-0.910,(Close-ts_Delay(Close,1))/Close,getNan(Close)))")

} // namespace generated_factors

#endif // GENERATED_FACTORS_FACTORP6TN9_HPP