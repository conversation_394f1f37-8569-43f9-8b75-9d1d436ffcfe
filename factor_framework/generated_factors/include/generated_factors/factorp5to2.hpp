#ifndef GENERATED_FACTORS_FACTORP5TO2_HPP
#define GENERATED_FACTORS_FACTORP5TO2_HPP

#include "factor_framework/factor_base.hpp"
#include "feature_operators.hpp"

namespace generated_factors {

/**
 * p5_to2 因子
 * ID: 58
 * 公式: ts_Corr(Amount,ts_Delay(Amount,1),60)
 */
class FactorP5to2 : public factor_framework::factor_base {
public:
    /**
     * 构造函数
     */
    FactorP5to2(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 计算因子值
     */
    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override;

    /**
     * 获取所需数据字段
     */
    std::vector<std::string> get_required_fields() const override;
};
REGISTER_FACTOR_SIMPLE(FactorP5to2, 58, "p5_to2", "ts_Corr(Amount,ts_Delay(Amount,1),60)")

} // namespace generated_factors

#endif // GENERATED_FACTORS_FACTORP5TO2_HPP