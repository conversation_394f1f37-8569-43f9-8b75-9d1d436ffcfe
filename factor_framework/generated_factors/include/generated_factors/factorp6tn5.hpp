#ifndef GENERATED_FACTORS_FACTORP6TN5_HPP
#define GENERATED_FACTORS_FACTORP6TN5_HPP

#include "factor_framework/factor_base.hpp"
#include "feature_operators.hpp"

namespace generated_factors {

/**
 * p6_tn5 因子
 * ID: 69
 * 公式: Tot_Mean(High-ts_Delay(Close,1))/Tot_Sum(ts_Delay(Close,1)-Low)
 */
class FactorP6tn5 : public factor_framework::factor_base {
public:
    /**
     * 构造函数
     */
    FactorP6tn5(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 计算因子值
     */
    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override;

    /**
     * 获取所需数据字段
     */
    std::vector<std::string> get_required_fields() const override;
};
REGISTER_FACTOR_SIMPLE(FactorP6tn5, 69, "p6_tn5", "Tot_Mean(High-ts_Delay(Close,1))/Tot_Sum(ts_Delay(Close,1)-Low)")

} // namespace generated_factors

#endif // GENERATED_FACTORS_FACTORP6TN5_HPP