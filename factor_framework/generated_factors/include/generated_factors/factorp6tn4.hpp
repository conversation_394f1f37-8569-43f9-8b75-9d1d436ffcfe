#ifndef GENERATED_FACTORS_FACTORP6TN4_HPP
#define GENERATED_FACTORS_FACTORP6TN4_HPP

#include "factor_framework/factor_base.hpp"
#include "feature_operators.hpp"

namespace generated_factors {

/**
 * p6_tn4 因子
 * ID: 68
 * 公式: Tot_Sum(IfThen(Close-ts_Delay(Close,1),1,0))
 */
class FactorP6tn4 : public factor_framework::factor_base {
public:
    /**
     * 构造函数
     */
    FactorP6tn4(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 计算因子值
     */
    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override;

    /**
     * 获取所需数据字段
     */
    std::vector<std::string> get_required_fields() const override;
};
REGISTER_FACTOR_SIMPLE(FactorP6tn4, 68, "p6_tn4", "Tot_Sum(IfThen(Close-ts_Delay(Close,1),1,0))")

} // namespace generated_factors

#endif // GENERATED_FACTORS_FACTORP6TN4_HPP