#ifndef GENERATED_FACTORS_FACTORP5TO7_HPP
#define GENERATED_FACTORS_FACTORP5TO7_HPP

#include "factor_framework/factor_base.hpp"
#include "feature_operators.hpp"

namespace generated_factors {

/**
 * p5_to7 因子
 * ID: 63
 * 公式: Tot_Sum(Abs(Close/ts_Delay(Close,1)-1)/(Close*Volume))
 */
class FactorP5to7 : public factor_framework::factor_base {
public:
    /**
     * 构造函数
     */
    FactorP5to7(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 计算因子值
     */
    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override;

    /**
     * 获取所需数据字段
     */
    std::vector<std::string> get_required_fields() const override;
};
REGISTER_FACTOR_SIMPLE(FactorP5to7, 63, "p5_to7", "Tot_Sum(Abs(Close/ts_Delay(Close,1)-1)/(Close*Volume))")

} // namespace generated_factors

#endif // GENERATED_FACTORS_FACTORP5TO7_HPP