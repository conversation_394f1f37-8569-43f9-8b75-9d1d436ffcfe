#ifndef GENERATED_FACTORS_FACTORP3MF1_HPP
#define GENERATED_FACTORS_FACTORP3MF1_HPP

#include "factor_framework/factor_base.hpp"
#include "feature_operators.hpp"

namespace generated_factors {

/**
 * p3_mf1 因子
 * ID: 37
 * 公式: Tot_Stdev(Abs(Close/Open-1)/Sqrt(Volume))
 */
class FactorP3mf1 : public factor_framework::factor_base {
public:
    /**
     * 构造函数
     */
    FactorP3mf1(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 计算因子值
     */
    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override;

    /**
     * 获取所需数据字段
     */
    std::vector<std::string> get_required_fields() const override;
};
REGISTER_FACTOR_SIMPLE(FactorP3mf1, 37, "p3_mf1", "Tot_Stdev(Abs(Close/Open-1)/Sqrt(Volume))")

} // namespace generated_factors

#endif // GENERATED_FACTORS_FACTORP3MF1_HPP