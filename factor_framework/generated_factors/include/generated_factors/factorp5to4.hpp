#ifndef GENERATED_FACTORS_FACTORP5TO4_HPP
#define GENERATED_FACTORS_FACTORP5TO4_HPP

#include "factor_framework/factor_base.hpp"
#include "feature_operators.hpp"

namespace generated_factors {

/**
 * p5_to4 因子
 * ID: 60
 * 公式: Tot_Mean(Abs(Close/ts_Delay(Close,1)-1)/Amount)
 */
class FactorP5to4 : public factor_framework::factor_base {
public:
    /**
     * 构造函数
     */
    FactorP5to4(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 计算因子值
     */
    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override;

    /**
     * 获取所需数据字段
     */
    std::vector<std::string> get_required_fields() const override;
};
REGISTER_FACTOR_SIMPLE(FactorP5to4, 60, "p5_to4", "Tot_Mean(Abs(Close/ts_Delay(Close,1)-1)/Amount)")

} // namespace generated_factors

#endif // GENERATED_FACTORS_FACTORP5TO4_HPP