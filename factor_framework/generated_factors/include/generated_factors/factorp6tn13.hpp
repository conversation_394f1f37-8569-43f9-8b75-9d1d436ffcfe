#ifndef GENERATED_FACTORS_FACTORP6TN13_HPP
#define GENERATED_FACTORS_FACTORP6TN13_HPP

#include "factor_framework/factor_base.hpp"
#include "feature_operators.hpp"

namespace generated_factors {

/**
 * p6_tn13 因子
 * ID: 77
 * 公式: Tot_Sum(IfThen(Close-ts_Delay(Close,1),Volume,-Volume))
 */
class FactorP6tn13 : public factor_framework::factor_base {
public:
    /**
     * 构造函数
     */
    FactorP6tn13(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 计算因子值
     */
    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override;

    /**
     * 获取所需数据字段
     */
    std::vector<std::string> get_required_fields() const override;
};
REGISTER_FACTOR_SIMPLE(FactorP6tn13, 77, "p6_tn13", "Tot_Sum(IfThen(Close-ts_Delay(Close,1),Volume,-Volume))")

} // namespace generated_factors

#endif // GENERATED_FACTORS_FACTORP6TN13_HPP