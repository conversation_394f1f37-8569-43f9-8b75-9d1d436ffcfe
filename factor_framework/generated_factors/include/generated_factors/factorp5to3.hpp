#ifndef GENERATED_FACTORS_FACTORP5TO3_HPP
#define GENERATED_FACTORS_FACTORP5TO3_HPP

#include "factor_framework/factor_base.hpp"
#include "feature_operators.hpp"

namespace generated_factors {

/**
 * p5_to3 因子
 * ID: 59
 * 公式: Tot_Sum(Amount)/(Tot_Sum(Abs(High-Low)/Close+Abs(Open-ts_Delay(Close,1)))*(1+Tot_Stdev(Close)/Tot_Mean(Close)))
 */
class FactorP5to3 : public factor_framework::factor_base {
public:
    /**
     * 构造函数
     */
    FactorP5to3(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 计算因子值
     */
    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override;

    /**
     * 获取所需数据字段
     */
    std::vector<std::string> get_required_fields() const override;
};
REGISTER_FACTOR_SIMPLE(FactorP5to3, 59, "p5_to3", "Tot_Sum(Amount)/(Tot_Sum(Abs(High-Low)/Close+Abs(Open-ts_Delay(Close,1)))*(1+Tot_Stdev(Close)/Tot_Mean(Close)))")

} // namespace generated_factors

#endif // GENERATED_FACTORS_FACTORP5TO3_HPP