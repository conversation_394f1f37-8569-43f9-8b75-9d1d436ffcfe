#ifndef GENERATED_FACTORS_FACTORP2ET16_HPP
#define GENERATED_FACTORS_FACTORP2ET16_HPP

#include "factor_framework/factor_base.hpp"
#include "feature_operators.hpp"

namespace generated_factors {

/**
 * p2_et16 因子
 * ID: 32
 * 公式: Tot_Mean(IfThen(-(Close/ts_Delay(Close,1)-1)+(Tot_Mean(Close/ts_Delay(Close,1)-1)-Tot_Stdev(Close/ts_Delay(Close,1)-1)),Close/ts_Delay(Close,1)-1,getNan(Close)))
 */
class FactorP2et16 : public factor_framework::factor_base {
public:
    /**
     * 构造函数
     */
    FactorP2et16(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 计算因子值
     */
    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override;

    /**
     * 获取所需数据字段
     */
    std::vector<std::string> get_required_fields() const override;
};
REGISTER_FACTOR_SIMPLE(FactorP2et16, 32, "p2_et16", "Tot_Mean(IfThen(-(Close/ts_Delay(Close,1)-1)+(Tot_Mean(Close/ts_Delay(Close,1)-1)-Tot_Stdev(Close/ts_Delay(Close,1)-1)),Close/ts_Delay(Close,1)-1,getNan(Close)))")

} // namespace generated_factors

#endif // GENERATED_FACTORS_FACTORP2ET16_HPP