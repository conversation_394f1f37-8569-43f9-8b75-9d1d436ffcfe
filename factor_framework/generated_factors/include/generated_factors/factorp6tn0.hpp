#ifndef GENERATED_FACTORS_FACTORP6TN0_HPP
#define GENERATED_FACTORS_FACTORP6TN0_HPP

#include "factor_framework/factor_base.hpp"
#include "feature_operators.hpp"

namespace generated_factors {

/**
 * p6_tn0 因子
 * ID: 64
 * 公式: Tot_Sum(IfThen(Close-(ts_Mean(Close,30)+ts_Stdev(Close,30)),-1,IfThen((ts_Mean(Close,30)-ts_Stdev(Close,30))-Close,1,0)))
 */
class FactorP6tn0 : public factor_framework::factor_base {
public:
    /**
     * 构造函数
     */
    FactorP6tn0(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 计算因子值
     */
    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override;

    /**
     * 获取所需数据字段
     */
    std::vector<std::string> get_required_fields() const override;
};
REGISTER_FACTOR_SIMPLE(FactorP6tn0, 64, "p6_tn0", "Tot_Sum(IfThen(Close-(ts_Mean(Close,30)+ts_Stdev(Close,30)),-1,IfThen((ts_Mean(Close,30)-ts_Stdev(Close,30))-Close,1,0)))")

} // namespace generated_factors

#endif // GENERATED_FACTORS_FACTORP6TN0_HPP