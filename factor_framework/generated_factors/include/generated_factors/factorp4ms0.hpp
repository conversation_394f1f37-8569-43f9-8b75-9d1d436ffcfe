#ifndef GENERATED_FACTORS_FACTORP4MS0_HPP
#define GENERATED_FACTORS_FACTORP4MS0_HPP

#include "factor_framework/factor_base.hpp"
#include "feature_operators.hpp"

namespace generated_factors {

/**
 * p4_ms0 因子
 * ID: 49
 * 公式: Tot_Mean(Close/ts_Delay(Close,1)-1-Log(Close/ts_Delay(Close,1)))
 */
class FactorP4ms0 : public factor_framework::factor_base {
public:
    /**
     * 构造函数
     */
    FactorP4ms0(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 计算因子值
     */
    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override;

    /**
     * 获取所需数据字段
     */
    std::vector<std::string> get_required_fields() const override;
};
REGISTER_FACTOR_SIMPLE(FactorP4ms0, 49, "p4_ms0", "Tot_Mean(Close/ts_Delay(Close,1)-1-Log(Close/ts_Delay(Close,1)))")

} // namespace generated_factors

#endif // GENERATED_FACTORS_FACTORP4MS0_HPP