#ifndef GENERATED_FACTORS_FACTORP6TN11_HPP
#define GENERATED_FACTORS_FACTORP6TN11_HPP

#include "factor_framework/factor_base.hpp"
#include "feature_operators.hpp"

namespace generated_factors {

/**
 * p6_tn11 因子
 * ID: 75
 * 公式: ts_Corr(Close-ts_Delay(Close,1),pn_Mean(Close-ts_Delay(Close,1)),60)
 */
class FactorP6tn11 : public factor_framework::factor_base {
public:
    /**
     * 构造函数
     */
    FactorP6tn11(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 计算因子值
     */
    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override;

    /**
     * 获取所需数据字段
     */
    std::vector<std::string> get_required_fields() const override;
};
REGISTER_FACTOR_SIMPLE(FactorP6tn11, 75, "p6_tn11", "ts_Corr(Close-ts_Delay(Close,1),pn_Mean(Close-ts_Delay(Close,1)),60)")

} // namespace generated_factors

#endif // GENERATED_FACTORS_FACTORP6TN11_HPP