[{"id": 0, "name": "p1_corrs0", "formula": "ts_Corr(Close,Volume,60)", "class_name": "FactorP1corrs0", "required_fields": ["Volume", "Close"]}, {"id": 1, "name": "p1_corrs1", "formula": "ts_<PERSON>rr(Close/ts_Delay(Close,1)-1,Volume,60)", "class_name": "FactorP1corrs1", "required_fields": ["Volume", "Close"]}, {"id": 2, "name": "p1_corrs2", "formula": "ts_<PERSON>rr(ts_<PERSON>ay(Close,1),Volume,60)", "class_name": "FactorP1corrs2", "required_fields": ["Volume", "Close"]}, {"id": 3, "name": "p1_corrs3", "formula": "ts_<PERSON><PERSON>(Close,ts_Delay(Volume,1),60)", "class_name": "FactorP1corrs3", "required_fields": ["Volume", "Close"]}, {"id": 4, "name": "p1_corrs4", "formula": "ts_<PERSON><PERSON>(ts_Delay(Close/ts_Delay(Close,1)-1,1),Close/ts_Delay(Close,1)-1,60)", "class_name": "FactorP1corrs4", "required_fields": ["Close"]}, {"id": 5, "name": "p1_corrs5", "formula": "ts_<PERSON><PERSON>(ts_Delay(Close/ts_Delay(Close,1)-1,1),Close,60)", "class_name": "FactorP1corrs5", "required_fields": ["Close"]}, {"id": 6, "name": "p1_corrs6", "formula": "ts_<PERSON><PERSON>(Volume,Volume-ts_Delay(Volume,1),60)", "class_name": "FactorP1corrs6", "required_fields": ["Volume"]}, {"id": 7, "name": "p1_corrs7", "formula": "ts_<PERSON><PERSON>(Volume-ts_Delay(Volume,1), Volume-ts_Delay(Volume,1) - ts_Delay(Volume-ts_Delay(Volume,1),1),60)", "class_name": "FactorP1corrs7", "required_fields": ["Volume"]}, {"id": 8, "name": "p1_corrs8", "formula": "ts_Corr(VWAP,Volume,60)", "class_name": "FactorP1corrs8", "required_fields": ["Volume", "VWAP"]}, {"id": 9, "name": "p1_corrs9", "formula": "ts_Corr(VWAP/ts_Delay(VWAP,1)-1,Volume,60)", "class_name": "FactorP1corrs9", "required_fields": ["Volume", "VWAP"]}, {"id": 10, "name": "p1_corrs10", "formula": "ts_Corr(ts_Delay(VWAP,1),Volume,60)", "class_name": "FactorP1corrs10", "required_fields": ["Volume", "VWAP"]}, {"id": 11, "name": "p1_corrs11", "formula": "ts_<PERSON>rr(VWAP,ts_Delay(Volume,1),60)", "class_name": "FactorP1corrs11", "required_fields": ["Volume", "VWAP"]}, {"id": 12, "name": "p1_corrs12", "formula": "ts_<PERSON>rr(ts_Delay(VWAP/ts_Delay(VWAP,1)-1,1),VWAP/ts_Delay(VWAP,1)-1,60)", "class_name": "FactorP1corrs12", "required_fields": ["VWAP"]}, {"id": 13, "name": "p1_corrs13", "formula": "ts_<PERSON>rr(ts_Delay(VWAP/ts_Delay(VWAP,1)-1,1),VWA<PERSON>,60)", "class_name": "FactorP1corrs13", "required_fields": ["VWAP"]}, {"id": 14, "name": "p1_corrs14", "formula": "ts_<PERSON><PERSON>(Volume,Volume-ts_Delay(Volume,1),60)", "class_name": "FactorP1corrs14", "required_fields": ["Volume"]}, {"id": 15, "name": "p1_corrs15", "formula": "ts_<PERSON><PERSON>(Volume-ts_Delay(Volume,1), Volume-ts_Delay(Volume,1) - ts_Delay(Volume-ts_Delay(Volume,1),1),60)", "class_name": "FactorP1corrs15", "required_fields": ["Volume"]}, {"id": 16, "name": "p2_et0", "formula": "Tot_Mean(IfThen(IfThen(Volume-ts_Delay(Volume,1)-Tot_<PERSON>(Volume-ts_Delay(Volume,1))-Tot_Stdev(Volume-ts_Delay(Volume,1)),1,0),Close/ts_Delay(Close,1)-1,get<PERSON><PERSON>(Close/ts_Delay(Close,1)-1)))", "class_name": "FactorP2et0", "required_fields": ["Volume", "Close"]}, {"id": 17, "name": "p2_et1", "formula": "Tot_Mean(IfThen(ts_Sum(IfThen(Tot_Rank(Volume-ts_Delay(Volume,1))-0.9,1,0),10),Close/ts_Delay(Close,1)-1,get<PERSON><PERSON>(Close/ts_Delay(Close,1)-1)))", "class_name": "FactorP2et1", "required_fields": ["Volume", "Close"]}, {"id": 18, "name": "p2_et2", "formula": "Tot_Stdev(IfThen(ts_Sum(IfThen(Tot_Rank(Close/ts_Delay(Close,1)-1)-0.9,1,0),10),Close/ts_Delay(Close,1)-1,get<PERSON><PERSON>(Close/ts_Delay(Close,1)-1)))", "class_name": "FactorP2et2", "required_fields": ["Close"]}, {"id": 19, "name": "p2_et3", "formula": "Tot_<PERSON>(IfThen(IfThen(Close/ts_Delay(Close,1)-1-<PERSON><PERSON>_<PERSON>(Close/ts_Delay(Close,1)-1)-To<PERSON>_Stdev(Close/ts_Delay(Close,1)-1),1,0),Close/ts_Delay(Close,1)-1,get<PERSON><PERSON>(Close/ts_Delay(Close,1)-1)))", "class_name": "FactorP2et3", "required_fields": ["Close"]}, {"id": 20, "name": "p2_et4", "formula": "Tot_<PERSON>(IfThen(ts_Sum(IfThen(Tot_Rank(Close/ts_Delay(Close,1)-1)-0.9,1,0),10),Close/ts_Delay(Close,1)-1,get<PERSON><PERSON>(Close/ts_Delay(Close,1)-1)))", "class_name": "FactorP2et4", "required_fields": ["Close"]}, {"id": 21, "name": "p2_et5", "formula": "ts_Stdev(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Stdev(Close/ts_Delay(Close,1)-1,60))", "class_name": "FactorP2et5", "required_fields": ["Close"]}, {"id": 22, "name": "p2_et6", "formula": "Abs(ts_Stdev(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Stdev(Close/ts_Delay(Close,1)-1,60)))", "class_name": "FactorP2et6", "required_fields": ["Close"]}, {"id": 23, "name": "p2_et7", "formula": "ts_Mean(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Mean(Close/ts_Delay(Close,1)-1,60))", "class_name": "FactorP2et7", "required_fields": ["Close"]}, {"id": 24, "name": "p2_et8", "formula": "Abs(ts_Mean(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Mean(Close/ts_Delay(Close,1)-1,60)))", "class_name": "FactorP2et8", "required_fields": ["Close"]}, {"id": 25, "name": "p2_et9", "formula": "ts_Mean((Close/ts_Delay(Close,1)-1)*IfThen(Volume-ts_Mean(Volume,30)-1.210*ts_Stdev(Volume,30),1,0),30)", "class_name": "FactorP2et9", "required_fields": ["Volume", "Close"]}, {"id": 26, "name": "p2_et10", "formula": "ts_Mean(IfThen((Close/ts_Delay(Close,1)-1),(Close/ts_Delay(Close,1)-1),0)*IfThen(Volume-ts_Mean(Volume,30)-0.10*ts_Stdev(Volume,30),1,0),30)", "class_name": "FactorP2et10", "required_fields": ["Volume", "Close"]}, {"id": 27, "name": "p2_et11", "formula": "To<PERSON>_<PERSON><PERSON>(IfThen(ts_Delay(ts_<PERSON>(Low,30),1)-Low,1,0))", "class_name": "FactorP2et11", "required_fields": ["Low"]}, {"id": 28, "name": "p2_et12", "formula": "Tot_Stdev(Abs(Close/ts_Delay(Close,1)-1-pn_Mean(Close/ts_Delay(Close,1)-1)))", "class_name": "FactorP2et12", "required_fields": ["Close"]}, {"id": 29, "name": "p2_et13", "formula": "Tot_Stdev(pn_Rank(Close/ts_Delay(Close,1)-1))", "class_name": "FactorP2et13", "required_fields": ["Close"]}, {"id": 30, "name": "p2_et14", "formula": "Tot_Stdev(pn_Rank(Volume))", "class_name": "FactorP2et14", "required_fields": ["Volume"]}, {"id": 31, "name": "p2_et15", "formula": "Tot_Mean(Abs(Close/ts_Delay(Close,1)-1-pn_Mean(Close/ts_Delay(Close,1)-1))/(Abs(pn_Mean(Close/ts_Delay(Close,1)-1))+Abs(Close/ts_Delay(Close,1)-1)+0.1))", "class_name": "FactorP2et15", "required_fields": ["Close"]}, {"id": 32, "name": "p2_et16", "formula": "Tot_Mean(IfThen(-(Close/ts_Delay(Close,1)-1)+(Tot_Mean(Close/ts_Delay(Close,1)-1)-Tot_Stdev(Close/ts_Delay(Close,1)-1)),Close/ts_Delay(Close,1)-1,get<PERSON><PERSON>(Close)))", "class_name": "FactorP2et16", "required_fields": ["Close"]}, {"id": 33, "name": "p2_et17", "formula": "Tot_Mean(IfThen(Tot_Rank(Close/ts_Delay(Close,1)-1)-0.93,Close/ts_Delay(Close,1)-1,get<PERSON><PERSON>(Close)))", "class_name": "FactorP2et17", "required_fields": ["Close"]}, {"id": 34, "name": "p2_et18", "formula": "Tot_Mean(IfThen(0.07-Tot_Rank(Close/ts_Delay(Close,1)-1),Close/ts_Delay(Close,1)-1,get<PERSON><PERSON>(Close)))", "class_name": "FactorP2et18", "required_fields": ["Close"]}, {"id": 35, "name": "p2_et19", "formula": "To<PERSON>_<PERSON>m(IfThen(Equal(Close/ts_Delay(Close,1)-1,0),1,get<PERSON><PERSON>(Close)))", "class_name": "FactorP2et19", "required_fields": ["Close"]}, {"id": 36, "name": "p3_mf0", "formula": "Tot_Mean(Abs(Close/Open-1)/Sqrt(Volume))", "class_name": "FactorP3mf0", "required_fields": ["Open", "Volume", "Close"]}, {"id": 37, "name": "p3_mf1", "formula": "Tot_Stdev(Abs(Close/Open-1)/Sqrt(Volume))", "class_name": "FactorP3mf1", "required_fields": ["Open", "Volume", "Close"]}, {"id": 38, "name": "p3_mf2", "formula": "Tot_Mean(IfThen(Tot_Rank(Abs(Close/Open-1)/Sqrt(Volume))-0.8,Close,get<PERSON>an(Close)))", "class_name": "FactorP3mf2", "required_fields": ["Open", "Volume", "Close"]}, {"id": 39, "name": "p3_mf3", "formula": "Tot_Mean(IfThen(Tot_Rank(Abs(Close/Open-1)+(Volume))-0.8,Close,get<PERSON>an(Close)))", "class_name": "FactorP3mf3", "required_fields": ["Open", "Volume", "Close"]}, {"id": 40, "name": "p3_mf4", "formula": "Tot_Mean(IfThen(Tot_Rank(Abs(Close/Open-1)/Log(Volume))-0.8,Close,get<PERSON>an(Close)))", "class_name": "FactorP3mf4", "required_fields": ["Open", "Volume", "Close"]}, {"id": 41, "name": "p3_mf5", "formula": "Tot_<PERSON>m(If<PERSON><PERSON>(Equal(Abs(Close-Open),A<PERSON>(High-Low)),Amount,get<PERSON><PERSON>(Close)))", "class_name": "FactorP3mf5", "required_fields": ["Amount", "High", "Low", "Close", "Open"]}, {"id": 42, "name": "p3_mf6", "formula": "Tot_Sum(IfThen(Tot_Rank(Abs(Close/Open-1)/Sqrt(Volume))-0.8,Volume,getNan(Close)))", "class_name": "FactorP3mf6", "required_fields": ["Open", "Volume", "Close"]}, {"id": 43, "name": "p3_mf7", "formula": "Tot_<PERSON>(ts_Stdev(Close/ts_Delay(Close,1)-1,10))", "class_name": "FactorP3mf7", "required_fields": ["Close"]}, {"id": 44, "name": "p3_mf8", "formula": "Tot_<PERSON>(ts_Stdev(ts_Stdev(Close/ts_Delay(Close,1)-1,10),10))", "class_name": "FactorP3mf8", "required_fields": ["Close"]}, {"id": 45, "name": "p3_mf9", "formula": "ts_<PERSON><PERSON>(ts_Stdev(ts_Stdev(Close/ts_Delay(Close,1)-1,10),10),Volume*Close,60)", "class_name": "FactorP3mf9", "required_fields": ["Volume", "Close"]}, {"id": 46, "name": "p3_mf10", "formula": "<PERSON><PERSON>_<PERSON><PERSON>(IfThen(ts_Stdev(ts_Stdev(Close/ts_Delay(Close,1)-1,10),10)-To<PERSON>_<PERSON>(ts_Stdev(ts_Stdev(Close/ts_Delay(Close,1)-1,10),10)),Volume*Close,get<PERSON>an(Close)))", "class_name": "FactorP3mf10", "required_fields": ["Volume", "Close"]}, {"id": 47, "name": "p3_mf11", "formula": "Tot_Sum(IfThen(Tot_Rank(Volume)-0.8,(Close-Open)/Open,get<PERSON>an(Close)))", "class_name": "FactorP3mf11", "required_fields": ["Open", "Volume", "Close"]}, {"id": 48, "name": "p3_mf12", "formula": "(Tot_Sum(IfThen(0.2-Tot_Rank(Volume),(Close-Open)/Open,get<PERSON>an(Close))))", "class_name": "FactorP3mf12", "required_fields": ["Open", "Volume", "Close"]}, {"id": 49, "name": "p4_ms0", "formula": "To<PERSON>_<PERSON>(Close/ts_Delay(Close,1)-1-Log(Close/ts_Delay(Close,1)))", "class_name": "FactorP4ms0", "required_fields": ["Close"]}, {"id": 50, "name": "p4_ms1", "formula": "Tot_Mean(Close/ts_Delay(Close,1)-1-Log(Close/ts_Delay(Close,1))-Power(Log(Close/ts_Delay(Close,1)),2)/2)", "class_name": "FactorP4ms1", "required_fields": ["Close"]}, {"id": 51, "name": "p4_ms2", "formula": "Tot_ArgMax(Close)", "class_name": "FactorP4ms2", "required_fields": ["Close"]}, {"id": 52, "name": "p4_ms3", "formula": "<PERSON><PERSON>_<PERSON><PERSON>(Close)", "class_name": "FactorP4ms3", "required_fields": ["Close"]}, {"id": 53, "name": "p4_ms4", "formula": "Tot_Sum(Power((Close-ts_Delay(Close,1))/Close,2))", "class_name": "FactorP4ms4", "required_fields": ["Close"]}, {"id": 54, "name": "p4_ms5", "formula": "Tot_Sum(Power((Close-ts_Delay(Close,1))/Close,3))", "class_name": "FactorP4ms5", "required_fields": ["Close"]}, {"id": 55, "name": "p4_ms6", "formula": "Tot_Sum(IfThen(Close/ts_Delay(Close,1)-1,Volume,get<PERSON><PERSON>(Close)))", "class_name": "FactorP4ms6", "required_fields": ["Volume", "Close"]}, {"id": 56, "name": "p5_to0", "formula": "Tot_<PERSON>m(IfThen(Close-ts_Delay(Close,1),Amount,-Amount))", "class_name": "FactorP5to0", "required_fields": ["Amount", "Close"]}, {"id": 57, "name": "p5_to1", "formula": "Tot_ArgMax(Volume)", "class_name": "FactorP5to1", "required_fields": ["Volume"]}, {"id": 58, "name": "p5_to2", "formula": "ts_<PERSON><PERSON>(<PERSON>ount,ts_<PERSON><PERSON>(Amount,1),60)", "class_name": "FactorP5to2", "required_fields": ["Amount"]}, {"id": 59, "name": "p5_to3", "formula": "Tot_Sum(Amount)/(Tot_Sum(Abs(High-Low)/Close+Abs(Open-ts_Delay(Close,1)))*(1+Tot_Stdev(Close)/Tot_Mean(Close)))", "class_name": "FactorP5to3", "required_fields": ["Amount", "High", "Low", "Close", "Open"]}, {"id": 60, "name": "p5_to4", "formula": "Tot_Mean(Abs(Close/ts_Delay(Close,1)-1)/Amount)", "class_name": "FactorP5to4", "required_fields": ["Amount", "Close"]}, {"id": 61, "name": "p5_to5", "formula": "Tot_Sum((High-Low)/Close)/Tot_Sum(Amount)", "class_name": "FactorP5to5", "required_fields": ["Amount", "High", "Low", "Close"]}, {"id": 62, "name": "p5_to6", "formula": "Tot_Sum((2*(High-Low)-Abs(Open-Close))/Close)/Tot_Sum(Amount)", "class_name": "FactorP5to6", "required_fields": ["Amount", "High", "Low", "Close", "Open"]}, {"id": 63, "name": "p5_to7", "formula": "Tot_Sum(Abs(Close/ts_Delay(Close,1)-1)/(Close*Volume))", "class_name": "FactorP5to7", "required_fields": ["Volume", "Close"]}, {"id": 64, "name": "p6_tn0", "formula": "Tot_<PERSON>m(IfThen(Close-(ts_Mean(Close,30)+ts_Stdev(Close,30)),-1,IfThen((ts_Mean(Close,30)-ts_Stdev(Close,30))-Close,1,0)))", "class_name": "FactorP6tn0", "required_fields": ["Close"]}, {"id": 65, "name": "p6_tn1", "formula": "<PERSON><PERSON>_<PERSON><PERSON>(High-Open)-<PERSON><PERSON>_<PERSON><PERSON>(Open-Low)", "class_name": "FactorP6tn1", "required_fields": ["Open", "High", "Low"]}, {"id": 66, "name": "p6_tn2", "formula": "ts_Regression(High,Low,60,'D')", "class_name": "FactorP6tn2", "required_fields": ["High", "Low"]}, {"id": 67, "name": "p6_tn3", "formula": "Tot_Mean(((High+Low)-ts_Delay(High+Low,1))*(High-Low)/2/Amount)", "class_name": "FactorP6tn3", "required_fields": ["Amount", "High", "Low"]}, {"id": 68, "name": "p6_tn4", "formula": "Tot_Sum(IfThen(Close-ts_Delay(Close,1),1,0))", "class_name": "FactorP6tn4", "required_fields": ["Close"]}, {"id": 69, "name": "p6_tn5", "formula": "Tot_<PERSON>(High-ts_Delay(Close,1))/Tot_Sum(ts_Delay(Close,1)-Low)", "class_name": "FactorP6tn5", "required_fields": ["High", "Low", "Close"]}, {"id": 70, "name": "p6_tn6", "formula": "Tot_Mean((ts_<PERSON>(High,30)-Close)/(ts_<PERSON>(High,30)-ts_<PERSON>(Low,30)))", "class_name": "FactorP6tn6", "required_fields": ["High", "Low", "Close"]}, {"id": 71, "name": "p6_tn7", "formula": "Tot_Mean((Close-ts_Min(Low,30))/(ts_<PERSON>(High,30)-ts_Min(Low,30)))", "class_name": "FactorP6tn7", "required_fields": ["High", "Low", "Close"]}, {"id": 72, "name": "p6_tn8", "formula": "Tot_Mean((High-Low)/Close)", "class_name": "FactorP6tn8", "required_fields": ["High", "Low", "Close"]}, {"id": 73, "name": "p6_tn9", "formula": "Tot_Mean(IfThen(Tot_Rank((Close-ts_Delay(Close,1))/Close)-0.910,(Close-ts_Delay(Close,1))/Close,get<PERSON>an(Close)))", "class_name": "FactorP6tn9", "required_fields": ["Close"]}, {"id": 74, "name": "p6_tn10", "formula": "Tot_Mean(IfThen(Tot_Rank(0.01-(Close-ts_Delay(Close,1))/Close),(Close-ts_Delay(Close,1))/Close,get<PERSON><PERSON>(Close)))", "class_name": "FactorP6tn10", "required_fields": ["Close"]}, {"id": 75, "name": "p6_tn11", "formula": "ts_<PERSON><PERSON>(Close-ts_Delay(Close,1),pn_<PERSON>(Close-ts_Delay(Close,1)),60)", "class_name": "FactorP6tn11", "required_fields": ["Close"]}, {"id": 76, "name": "p6_tn12", "formula": "Tot_Sum(IfThen(Tot_Rank(High-Low)-0.10,Volume,0))-Tot_Sum(IfThen(Tot_Rank(High-Low)-0.10,0,Volume))", "class_name": "FactorP6tn12", "required_fields": ["Volume", "High", "Low"]}, {"id": 77, "name": "p6_tn13", "formula": "To<PERSON>_<PERSON>m(IfThen(Close-ts_Delay(Close,1),Volume,-Volume))", "class_name": "FactorP6tn13", "required_fields": ["Volume", "Close"]}]