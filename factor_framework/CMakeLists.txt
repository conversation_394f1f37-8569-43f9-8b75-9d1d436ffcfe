cmake_minimum_required(VERSION 3.15)
project(FactorFramework VERSION 1.0 LANGUAGES CXX)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# 编译选项
add_compile_options(-Wall -O3 -g)

# 查找依赖包
find_package(Eigen3 REQUIRED CONFIG)

# 设置feature_operators路径
set(FEATURE_OPERATORS_DIR "${CMAKE_CURRENT_SOURCE_DIR}/../feature_operators")

# 检查feature_operators目录是否存在
if(NOT EXISTS "${FEATURE_OPERATORS_DIR}")
    message(FATAL_ERROR "feature_operators directory not found at: ${FEATURE_OPERATORS_DIR}")
endif()

# 添加feature_operators子目录
add_subdirectory(${FEATURE_OPERATORS_DIR} feature_operators_build)

# 添加生成的因子子目录
add_subdirectory(generated_factors)

# 创建factor_framework库
add_library(factor_framework_lib STATIC
    src/factor_base.cpp
    src/factor_manager.cpp
    src/data_interface.cpp
    src/factor_engine.cpp
    src/factor_framework.cpp
)

# 设置包含目录
target_include_directories(factor_framework_lib PUBLIC
    include
    ${FEATURE_OPERATORS_DIR}/include
)

# 链接依赖库
target_link_libraries(factor_framework_lib PUBLIC
    Eigen3::Eigen
    feature_ops_lib
)

# 创建demo可执行文件
add_executable(architecture_demo
    examples/architecture_demo.cpp
)

# 设置demo的包含目录
target_include_directories(architecture_demo PRIVATE
    include
    generated_factors/include
    ${FEATURE_OPERATORS_DIR}/include
)

# 链接demo的依赖库
target_link_libraries(architecture_demo
    factor_framework_lib
    generated_factors
    feature_ops_lib
    Eigen3::Eigen
)

# 创建简单测试可执行文件
add_executable(simple_test
    examples/simple_test.cpp
)

# 设置简单测试的包含目录
target_include_directories(simple_test PRIVATE
    include
    generated_factors/include
    ${FEATURE_OPERATORS_DIR}/include
)

# 链接简单测试的依赖库
target_link_libraries(simple_test
    factor_framework_lib
    generated_factors
    feature_ops_lib
    Eigen3::Eigen
)

# 输出信息
message(STATUS "Factor Framework Configuration:")
message(STATUS "  Feature Operators Dir: ${FEATURE_OPERATORS_DIR}")
message(STATUS "  Eigen3 Include Dir: ${EIGEN3_INCLUDE_DIR}")
message(STATUS "  Build Type: ${CMAKE_BUILD_TYPE}")
