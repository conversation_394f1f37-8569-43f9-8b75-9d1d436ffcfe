#include "generated_factors/all_factors.hpp"
#include <iostream>
#include <algorithm>

namespace generated_factors {

std::vector<std::string> get_all_generated_factor_names() {
    return {"p2_et16"};
}

std::vector<int> get_all_generated_factor_ids() {
    return {32};
}

void initialize_all_factors() {
    // 这个函数确保所有因子类被链接器包含
    static bool initialized = false;
    if (!initialized) {
        std::cout << "✓ Generated factors library initialized" << std::endl;
        std::cout << "✓ Available factors: " << 1 << std::endl;

        // 显示所有可用的因子
        auto factor_names = get_all_generated_factor_names();
        for (const auto& factor_name : factor_names) {
            std::cout << "  - " << factor_name << std::endl;
        }

        initialized = true;
    }
}

/**
 * 批量注册所有生成的因子到factor_manager
 * @param manager 因子管理器
 * @return 成功注册的因子数量
 */
int register_all_factors_to_manager(std::shared_ptr<factor_framework::factor_manager> manager) {
    if (!manager) {
        std::cerr << "Error: factor_manager is null" << std::endl;
        return 0;
    }

    // 从静态注册列表创建所有因子实例
    auto& registration_list = factor_framework::get_factor_registration_list();

    int registered_count = 0;
    for (auto& creator : registration_list) {
        try {
            auto factor = creator();
            if (factor && manager->register_factor(std::move(factor))) {
                registered_count++;
            }
        } catch (const std::exception& e) {
            std::cerr << "Error creating factor: " << e.what() << std::endl;
        }
    }

    std::cout << "✓ Registered " << registered_count << " generated factors to manager" << std::endl;
    return registered_count;
}

} // namespace generated_factors