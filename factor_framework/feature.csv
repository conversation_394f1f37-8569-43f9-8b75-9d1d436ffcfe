,fname,forms
0,p1_corrs0,"ts_Corr(Close,Volume,60)"
1,p1_corrs1,"ts_Corr(Close/ts_Delay(Close,1)-1,Volume,60)"
2,p1_corrs2,"ts_Corr(ts_Delay(Close,1),Volume,60)"
3,p1_corrs3,"ts_Corr(Close,ts_Delay(Volume,1),60)"
4,p1_corrs4,"ts_Corr(ts_Delay(Close/ts_Delay(Close,1)-1,1),Close/ts_Delay(Close,1)-1,60)"
5,p1_corrs5,"ts_Corr(ts_Delay(Close/ts_Delay(Close,1)-1,1),Close,60)"
6,p1_corrs6,"ts_Corr(Volume,Volume-ts_Delay(Volume,1),60)"
7,p1_corrs7,"ts_Corr(Volume-ts_Delay(Volume,1), Volume-ts_Delay(Volume,1) - ts_Delay(Volume-ts_Delay(Volume,1),1),60)"
8,p1_corrs8,"ts_Corr(VWAP,Volume,60)"
9,p1_corrs9,"ts_Corr(VWAP/ts_Delay(VWAP,1)-1,Volume,60)"
10,p1_corrs10,"ts_Corr(ts_Delay(VWAP,1),Volume,60)"
11,p1_corrs11,"ts_Corr(VWAP,ts_Delay(Volume,1),60)"
12,p1_corrs12,"ts_Corr(ts_Delay(VWAP/ts_Delay(VWAP,1)-1,1),VWAP/ts_Delay(VWAP,1)-1,60)"
13,p1_corrs13,"ts_Corr(ts_Delay(VWAP/ts_Delay(VWAP,1)-1,1),VWAP,60)"
14,p1_corrs14,"ts_Corr(Volume,Volume-ts_Delay(Volume,1),60)"
15,p1_corrs15,"ts_Corr(Volume-ts_Delay(Volume,1), Volume-ts_Delay(Volume,1) - ts_Delay(Volume-ts_Delay(Volume,1),1),60)"
16,p2_et0,"Tot_Mean(IfThen(IfThen(Volume-ts_Delay(Volume,1)-Tot_Mean(Volume-ts_Delay(Volume,1))-Tot_Stdev(Volume-ts_Delay(Volume,1)),1,0),Close/ts_Delay(Close,1)-1,getNan(Close/ts_Delay(Close,1)-1)))"
17,p2_et1,"Tot_Mean(IfThen(ts_Sum(IfThen(Tot_Rank(Volume-ts_Delay(Volume,1))-0.9,1,0),10),Close/ts_Delay(Close,1)-1,getNan(Close/ts_Delay(Close,1)-1)))"
18,p2_et2,"Tot_Stdev(IfThen(ts_Sum(IfThen(Tot_Rank(Close/ts_Delay(Close,1)-1)-0.9,1,0),10),Close/ts_Delay(Close,1)-1,getNan(Close/ts_Delay(Close,1)-1)))"
19,p2_et3,"Tot_Mean(IfThen(IfThen(Close/ts_Delay(Close,1)-1-Tot_Mean(Close/ts_Delay(Close,1)-1)-Tot_Stdev(Close/ts_Delay(Close,1)-1),1,0),Close/ts_Delay(Close,1)-1,getNan(Close/ts_Delay(Close,1)-1)))"
20,p2_et4,"Tot_Mean(IfThen(ts_Sum(IfThen(Tot_Rank(Close/ts_Delay(Close,1)-1)-0.9,1,0),10),Close/ts_Delay(Close,1)-1,getNan(Close/ts_Delay(Close,1)-1)))"
21,p2_et5,"ts_Stdev(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Stdev(Close/ts_Delay(Close,1)-1,60))"
22,p2_et6,"Abs(ts_Stdev(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Stdev(Close/ts_Delay(Close,1)-1,60)))"
23,p2_et7,"ts_Mean(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Mean(Close/ts_Delay(Close,1)-1,60))"
24,p2_et8,"Abs(ts_Mean(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Mean(Close/ts_Delay(Close,1)-1,60)))"
25,p2_et9,"ts_Mean((Close/ts_Delay(Close,1)-1)*IfThen(Volume-ts_Mean(Volume,30)-1.210*ts_Stdev(Volume,30),1,0),30)"
26,p2_et10,"ts_Mean(IfThen((Close/ts_Delay(Close,1)-1),(Close/ts_Delay(Close,1)-1),0)*IfThen(Volume-ts_Mean(Volume,30)-0.10*ts_Stdev(Volume,30),1,0),30)"
27,p2_et11,"Tot_Sum(IfThen(ts_Delay(ts_Min(Low,30),1)-Low,1,0))"
28,p2_et12,"Tot_Stdev(Abs(Close/ts_Delay(Close,1)-1-pn_Mean(Close/ts_Delay(Close,1)-1)))"
29,p2_et13,"Tot_Stdev(pn_Rank(Close/ts_Delay(Close,1)-1))"
30,p2_et14,Tot_Stdev(pn_Rank(Volume))
31,p2_et15,"Tot_Mean(Abs(Close/ts_Delay(Close,1)-1-pn_Mean(Close/ts_Delay(Close,1)-1))/(Abs(pn_Mean(Close/ts_Delay(Close,1)-1))+Abs(Close/ts_Delay(Close,1)-1)+0.1))"
32,p2_et16,"Tot_Mean(IfThen(-(Close/ts_Delay(Close,1)-1)+(Tot_Mean(Close/ts_Delay(Close,1)-1)-Tot_Stdev(Close/ts_Delay(Close,1)-1)),Close/ts_Delay(Close,1)-1,getNan(Close)))"
33,p2_et17,"Tot_Mean(IfThen(Tot_Rank(Close/ts_Delay(Close,1)-1)-0.93,Close/ts_Delay(Close,1)-1,getNan(Close)))"
34,p2_et18,"Tot_Mean(IfThen(0.07-Tot_Rank(Close/ts_Delay(Close,1)-1),Close/ts_Delay(Close,1)-1,getNan(Close)))"
35,p2_et19,"Tot_Sum(IfThen(Equal(Close/ts_Delay(Close,1)-1,0),1,getNan(Close)))"
36,p3_mf0,Tot_Mean(Abs(Close/Open-1)/Sqrt(Volume))
37,p3_mf1,Tot_Stdev(Abs(Close/Open-1)/Sqrt(Volume))
38,p3_mf2,"Tot_Mean(IfThen(Tot_Rank(Abs(Close/Open-1)/Sqrt(Volume))-0.8,Close,getNan(Close)))"
39,p3_mf3,"Tot_Mean(IfThen(Tot_Rank(Abs(Close/Open-1)+(Volume))-0.8,Close,getNan(Close)))"
40,p3_mf4,"Tot_Mean(IfThen(Tot_Rank(Abs(Close/Open-1)/Log(Volume))-0.8,Close,getNan(Close)))"
41,p3_mf5,"Tot_Sum(IfThen(Equal(Abs(Close-Open),Abs(High-Low)),Amount,getNan(Close)))"
42,p3_mf6,"Tot_Sum(IfThen(Tot_Rank(Abs(Close/Open-1)/Sqrt(Volume))-0.8,Volume,getNan(Close)))"
43,p3_mf7,"Tot_Mean(ts_Stdev(Close/ts_Delay(Close,1)-1,10))"
44,p3_mf8,"Tot_Mean(ts_Stdev(ts_Stdev(Close/ts_Delay(Close,1)-1,10),10))"
45,p3_mf9,"ts_Corr(ts_Stdev(ts_Stdev(Close/ts_Delay(Close,1)-1,10),10),Volume*Close,60)"
46,p3_mf10,"Tot_Sum(IfThen(ts_Stdev(ts_Stdev(Close/ts_Delay(Close,1)-1,10),10)-Tot_Mean(ts_Stdev(ts_Stdev(Close/ts_Delay(Close,1)-1,10),10)),Volume*Close,getNan(Close)))"
47,p3_mf11,"Tot_Sum(IfThen(Tot_Rank(Volume)-0.8,(Close-Open)/Open,getNan(Close)))"
48,p3_mf12,"(Tot_Sum(IfThen(0.2-Tot_Rank(Volume),(Close-Open)/Open,getNan(Close))))"
49,p4_ms0,"Tot_Mean(Close/ts_Delay(Close,1)-1-Log(Close/ts_Delay(Close,1)))"
50,p4_ms1,"Tot_Mean(Close/ts_Delay(Close,1)-1-Log(Close/ts_Delay(Close,1))-Power(Log(Close/ts_Delay(Close,1)),2)/2)"
51,p4_ms2,Tot_ArgMax(Close)
52,p4_ms3,Tot_ArgMin(Close)
53,p4_ms4,"Tot_Sum(Power((Close-ts_Delay(Close,1))/Close,2))"
54,p4_ms5,"Tot_Sum(Power((Close-ts_Delay(Close,1))/Close,3))"
55,p4_ms6,"Tot_Sum(IfThen(Close/ts_Delay(Close,1)-1,Volume,getNan(Close)))"
56,p5_to0,"Tot_Sum(IfThen(Close-ts_Delay(Close,1),Amount,-Amount))"
57,p5_to1,Tot_ArgMax(Volume)
58,p5_to2,"ts_Corr(Amount,ts_Delay(Amount,1),60)"
59,p5_to3,"Tot_Sum(Amount)/(Tot_Sum(Abs(High-Low)/Close+Abs(Open-ts_Delay(Close,1)))*(1+Tot_Stdev(Close)/Tot_Mean(Close)))"
60,p5_to4,"Tot_Mean(Abs(Close/ts_Delay(Close,1)-1)/Amount)"
61,p5_to5,Tot_Sum((High-Low)/Close)/Tot_Sum(Amount)
62,p5_to6,Tot_Sum((2*(High-Low)-Abs(Open-Close))/Close)/Tot_Sum(Amount)
63,p5_to7,"Tot_Sum(Abs(Close/ts_Delay(Close,1)-1)/(Close*Volume))"
64,p6_tn0,"Tot_Sum(IfThen(Close-(ts_Mean(Close,30)+ts_Stdev(Close,30)),-1,IfThen((ts_Mean(Close,30)-ts_Stdev(Close,30))-Close,1,0)))"
65,p6_tn1,Tot_Sum(High-Open)-Tot_Sum(Open-Low)
66,p6_tn2,"ts_Regression(High,Low,60,'D')"
67,p6_tn3,"Tot_Mean(((High+Low)-ts_Delay(High+Low,1))*(High-Low)/2/Amount)"
68,p6_tn4,"Tot_Sum(IfThen(Close-ts_Delay(Close,1),1,0))"
69,p6_tn5,"Tot_Mean(High-ts_Delay(Close,1))/Tot_Sum(ts_Delay(Close,1)-Low)"
70,p6_tn6,"Tot_Mean((ts_Max(High,30)-Close)/(ts_Max(High,30)-ts_Min(Low,30)))"
71,p6_tn7,"Tot_Mean((Close-ts_Min(Low,30))/(ts_Max(High,30)-ts_Min(Low,30)))"
72,p6_tn8,Tot_Mean((High-Low)/Close)
73,p6_tn9,"Tot_Mean(IfThen(Tot_Rank((Close-ts_Delay(Close,1))/Close)-0.910,(Close-ts_Delay(Close,1))/Close,getNan(Close)))"
74,p6_tn10,"Tot_Mean(IfThen(Tot_Rank(0.01-(Close-ts_Delay(Close,1))/Close),(Close-ts_Delay(Close,1))/Close,getNan(Close)))"
75,p6_tn11,"ts_Corr(Close-ts_Delay(Close,1),pn_Mean(Close-ts_Delay(Close,1)),60)"
76,p6_tn12,"Tot_Sum(IfThen(Tot_Rank(High-Low)-0.10,Volume,0))-Tot_Sum(IfThen(Tot_Rank(High-Low)-0.10,0,Volume))"
77,p6_tn13,"Tot_Sum(IfThen(Close-ts_Delay(Close,1),Volume,-Volume))"

