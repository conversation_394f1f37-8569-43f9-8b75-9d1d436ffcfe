#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试公共子表达式优化功能
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from optimized_factor_code_generator import (
    ExpressionParser,
    CommonSubexpressionExtractor,
    OptimizedFormulaParser
)

def test_expression_parser():
    """测试表达式解析器"""
    print("=== 测试表达式解析器 ===")

    parser = ExpressionParser()

    # 测试简单表达式
    test_cases = [
        "Close",
        "ts_Delay(Close,1)",
        "Close/ts_Delay(Close,1)-1",
        "ts_Stdev(Close/ts_Delay(Close,1)-1,60)",
        "ts_Stdev(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Stdev(Close/ts_Delay(Close,1)-1,60))"
    ]

    for formula in test_cases:
        print(f"\n公式: {formula}")
        try:
            ast_node = parser.parse(formula)
            print(f"解析成功: {ast_node.node_type} - {ast_node.value}")
            print(f"哈希值: {ast_node.hash_value}")
            print(f"转换为C++: {ast_node.to_string()}")

            # 递归打印AST结构
            def print_ast(node, indent=0):
                prefix = "  " * indent
                print(f"{prefix}节点: {node.node_type} - {node.value} (hash: {node.hash_value})")
                for child in node.children:
                    print_ast(child, indent + 1)

            print("AST结构:")
            print_ast(ast_node)

        except Exception as e:
            print(f"解析失败: {e}")
            import traceback
            traceback.print_exc()

def test_cse_extraction():
    """测试公共子表达式提取"""
    print("\n=== 测试公共子表达式提取 ===")

    parser = ExpressionParser()
    extractor = CommonSubexpressionExtractor()

    # 测试p2_et5的公式
    formula = "ts_Stdev(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Stdev(Close/ts_Delay(Close,1)-1,60))"
    print(f"\n测试公式: {formula}")

    try:
        # 解析为AST
        ast_root = parser.parse(formula)
        print(f"根节点: {ast_root.node_type} - {ast_root.value}")

        # 提取公共子表达式
        common_subexprs, optimized_root = extractor.extract_common_subexpressions(ast_root)

        print(f"\n找到 {len(common_subexprs)} 个公共子表达式:")
        for var_name, node in common_subexprs:
            print(f"  {var_name}: {node.to_string()}")

        print(f"\n优化后的表达式: {optimized_root.to_string()}")

    except Exception as e:
        print(f"提取失败: {e}")
        import traceback
        traceback.print_exc()

def test_optimized_parser():
    """测试优化的公式解析器"""
    print("\n=== 测试优化的公式解析器 ===")

    parser = OptimizedFormulaParser()

    test_formulas = [
        "ts_Stdev(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Stdev(Close/ts_Delay(Close,1)-1,60))",
        "Abs(ts_Stdev(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Stdev(Close/ts_Delay(Close,1)-1,60)))",
        "ts_Corr(ts_Delay(Close/ts_Delay(Close,1)-1,1),Close/ts_Delay(Close,1)-1,60)"
    ]

    for formula in test_formulas:
        print(f"\n测试公式: {formula}")
        try:
            variable_declarations, final_expression = parser.parse_formula_with_optimization(formula)

            print(f"变量声明数量: {len(variable_declarations)}")
            for var_decl in variable_declarations:
                print(f"  {var_decl}")

            print(f"最终表达式: {final_expression}")

            # 生成完整的C++代码
            print("\n生成的C++代码:")
            for var_decl in variable_declarations:
                print(f"        {var_decl}")
            print(f"        auto result = {final_expression};")

        except Exception as e:
            print(f"优化失败: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    test_expression_parser()
    test_cse_extraction()
    test_optimized_parser()
    print("\n=== 测试完成 ===")
