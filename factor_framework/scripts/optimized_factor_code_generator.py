#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版因子代码自动生成器

这个脚本从feature.csv文件读取因子定义，自动生成对应的C++因子类。
使用AST分析提取公共子表达式，生成优化的C++代码。

生成的代码包括：
1. 因子头文件
2. 因子实现文件（包含公共子表达式优化）
3. 因子注册代码
4. CMake构建配置

使用方法：
    python optimized_factor_code_generator.py --input feature.csv --output_dir generated_factors

作者：AI Assistant
版本：2.0.0 - 优化版本
"""

import os
import sys
import csv
import re
import argparse
import ast
from typing import List, Dict, Tuple, Set, Optional, Union
from pathlib import Path
import json
from collections import defaultdict, Counter
import hashlib
from dataclasses import dataclass


@dataclass
class ASTNode:
    """AST节点类，用于表示表达式树"""
    node_type: str  # 'operator', 'field', 'constant', 'binary_op', 'variable'
    value: str      # 操作符名称、字段名或常量值
    children: List['ASTNode'] = None
    hash_value: str = None

    def __post_init__(self):
        if self.children is None:
            self.children = []
        # 计算节点的哈希值，用于识别相同的子表达式
        self.hash_value = self._compute_hash()

    def _compute_hash(self) -> str:
        """计算节点的哈希值"""
        content = f"{self.node_type}:{self.value}"
        if self.children:
            child_hashes = [child.hash_value for child in self.children]
            content += ":" + ":".join(sorted(child_hashes))
        return hashlib.md5(content.encode()).hexdigest()[:8]

    def to_string(self) -> str:
        """将AST节点转换为字符串表示"""
        if self.node_type == 'field':
            return f'get_field(data_map, "{self.value}")'
        elif self.node_type == 'constant':
            return self.value
        elif self.node_type == 'variable':
            return self.value
        elif self.node_type == 'operator':
            if not self.children:
                return f"feature_operators::{self.value}()"
            child_strs = [child.to_string() for child in self.children]
            return f"feature_operators::{self.value}({','.join(child_strs)})"
        elif self.node_type == 'binary_op':
            if len(self.children) == 2:
                left = self.children[0].to_string()
                right = self.children[1].to_string()
                return f"({left}{self.value}{right})"
            else:
                return str(self.value)
        else:
            return str(self.value)

    def __eq__(self, other):
        if not isinstance(other, ASTNode):
            return False
        return self.hash_value == other.hash_value

    def __hash__(self):
        return hash(self.hash_value)


class CommonSubexpressionExtractor:
    """公共子表达式提取器"""

    def __init__(self):
        self.subexpr_count = defaultdict(int)  # 子表达式出现次数
        self.subexpr_nodes = {}  # 哈希值到节点的映射
        self.variable_counter = 0  # 临时变量计数器

    def extract_common_subexpressions(self, root: ASTNode, min_count: int = 2) -> Tuple[List[Tuple[str, ASTNode]], ASTNode]:
        """
        提取公共子表达式
        返回: (公共子表达式列表, 优化后的根节点)
        """
        # 重置计数器
        self.subexpr_count.clear()
        self.subexpr_nodes.clear()
        self.variable_counter = 0

        # 第一遍：统计所有子表达式的出现次数
        self._count_subexpressions(root)

        # 找出出现次数 >= min_count 的子表达式
        common_subexprs = []
        var_mapping = {}  # 哈希值到变量名的映射

        for hash_val, count in self.subexpr_count.items():
            if count >= min_count and hash_val in self.subexpr_nodes:
                node = self.subexpr_nodes[hash_val]
                # 只提取复杂的子表达式（不是简单的字段或常量）
                if self._is_complex_expression(node):
                    var_name = f"p{self.variable_counter}"
                    self.variable_counter += 1
                    common_subexprs.append((var_name, node))
                    var_mapping[hash_val] = var_name

        # 第二遍：替换公共子表达式
        optimized_root = self._replace_subexpressions(root, var_mapping)

        return common_subexprs, optimized_root

    def _count_subexpressions(self, node: ASTNode):
        """递归统计子表达式出现次数"""
        if node is None:
            return

        # 记录当前节点
        self.subexpr_count[node.hash_value] += 1
        self.subexpr_nodes[node.hash_value] = node

        # 递归处理子节点
        for child in node.children:
            self._count_subexpressions(child)

    def _is_complex_expression(self, node: ASTNode) -> bool:
        """判断是否为复杂表达式（值得提取的子表达式）"""
        if node.node_type in ['field', 'constant', 'variable']:
            return False
        if node.node_type == 'operator' and len(node.children) == 0:
            return False
        # 至少包含一个操作符才认为是复杂表达式
        return True

    def _replace_subexpressions(self, node: ASTNode, var_mapping: Dict[str, str]) -> ASTNode:
        """递归替换公共子表达式为变量"""
        if node is None:
            return None

        # 如果当前节点是公共子表达式，替换为变量
        if node.hash_value in var_mapping:
            var_name = var_mapping[node.hash_value]
            return ASTNode('variable', var_name, [])

        # 递归处理子节点
        new_children = []
        for child in node.children:
            new_child = self._replace_subexpressions(child, var_mapping)
            new_children.append(new_child)

        # 创建新节点
        new_node = ASTNode(node.node_type, node.value, new_children)
        return new_node


class ExpressionParser:
    """表达式解析器 - 将字符串公式解析为AST"""

    def __init__(self):
        # 支持的操作符
        self.operators = {
            'ts_Delay', 'ts_Mean', 'ts_Stdev', 'ts_Delta', 'ts_Min', 'ts_Max',
            'ts_Corr', 'ts_Sum', 'ts_Rank', 'ts_Scale', 'ts_Regression',
            'pn_Rank', 'pn_Mean', 'pn_TransStd',
            'Tot_Mean', 'Tot_Sum', 'Tot_Stdev', 'Tot_Rank', 'Tot_ArgMax', 'Tot_ArgMin',
            'IfThen', 'Abs', 'Log', 'Sqrt', 'Equal', 'getNan', 'Power'
        }

        # 市场数据字段
        self.market_fields = {'Close', 'Open', 'High', 'Low', 'Volume', 'Amount', 'VWAP'}

    def parse(self, formula: str) -> ASTNode:
        """解析公式字符串为AST"""
        # 预处理：移除空格
        formula = formula.strip()
        tokens = self._tokenize(formula)
        return self._parse_tokens(tokens)

    def _tokenize(self, formula: str) -> List[str]:
        """将公式分解为token"""
        tokens = []
        i = 0
        while i < len(formula):
            if formula[i].isspace():
                i += 1
                continue
            elif formula[i] in '()+-*/,':
                tokens.append(formula[i])
                i += 1
            elif formula[i].isdigit() or formula[i] == '.':
                # 解析数字
                start = i
                while i < len(formula) and (formula[i].isdigit() or formula[i] == '.'):
                    i += 1
                tokens.append(formula[start:i])
            elif formula[i].isalpha() or formula[i] == '_':
                # 解析标识符（函数名或字段名）
                start = i
                while i < len(formula) and (formula[i].isalnum() or formula[i] == '_'):
                    i += 1
                tokens.append(formula[start:i])
            else:
                i += 1
        return tokens

    def _parse_tokens(self, tokens: List[str]) -> ASTNode:
        """从token列表解析表达式"""
        if not tokens:
            return ASTNode('constant', '0')

        # 使用递归下降解析器
        self.tokens = tokens
        self.pos = 0
        return self._parse_expression_tokens()

    def _parse_expression_tokens(self) -> ASTNode:
        """解析表达式（处理加减法）"""
        left = self._parse_term()

        while self.pos < len(self.tokens) and self.tokens[self.pos] in ['+', '-']:
            op = self.tokens[self.pos]
            self.pos += 1
            right = self._parse_term()
            left = ASTNode('binary_op', op, [left, right])

        return left

    def _parse_term(self) -> ASTNode:
        """解析项（处理乘除法）"""
        left = self._parse_factor()

        while self.pos < len(self.tokens) and self.tokens[self.pos] in ['*', '/']:
            op = self.tokens[self.pos]
            self.pos += 1
            right = self._parse_factor()
            left = ASTNode('binary_op', op, [left, right])

        return left

    def _parse_factor(self) -> ASTNode:
        """解析因子（处理括号、函数调用、字段、常量）"""
        if self.pos >= len(self.tokens):
            return ASTNode('constant', '0')

        token = self.tokens[self.pos]

        # 处理括号
        if token == '(':
            self.pos += 1
            expr = self._parse_expression_tokens()
            if self.pos < len(self.tokens) and self.tokens[self.pos] == ')':
                self.pos += 1
            return expr

        # 处理函数调用
        if token in self.operators and self.pos + 1 < len(self.tokens) and self.tokens[self.pos + 1] == '(':
            return self._parse_function_call()

        # 处理字段
        if token in self.market_fields:
            self.pos += 1
            return ASTNode('field', token)

        # 处理数字常量
        if self._is_number(token):
            self.pos += 1
            return ASTNode('constant', token)

        # 其他情况作为常量处理
        self.pos += 1
        return ASTNode('constant', token)

    def _parse_function_call(self) -> ASTNode:
        """解析函数调用"""
        func_name = self.tokens[self.pos]
        self.pos += 1  # 跳过函数名

        if self.pos >= len(self.tokens) or self.tokens[self.pos] != '(':
            return ASTNode('constant', func_name)

        self.pos += 1  # 跳过 '('

        # 解析参数
        args = []
        if self.pos < len(self.tokens) and self.tokens[self.pos] != ')':
            args.append(self._parse_expression_tokens())

            while self.pos < len(self.tokens) and self.tokens[self.pos] == ',':
                self.pos += 1  # 跳过 ','
                args.append(self._parse_expression_tokens())

        if self.pos < len(self.tokens) and self.tokens[self.pos] == ')':
            self.pos += 1  # 跳过 ')'

        return ASTNode('operator', func_name, args)

    def _is_number(self, s: str) -> bool:
        """检查字符串是否为数字"""
        try:
            float(s)
            return True
        except ValueError:
            return False


class FactorInfo:
    """因子信息类"""
    def __init__(self, factor_id: int, factor_name: str, formula: str):
        self.factor_id = factor_id
        self.factor_name = factor_name
        self.formula = formula
        self.required_fields = self._extract_required_fields()
        self.class_name = self._generate_class_name()

    def _extract_required_fields(self) -> Set[str]:
        """从公式中提取所需的数据字段"""
        # 常见的市场数据字段
        market_fields = {'Close', 'Open', 'High', 'Low', 'Volume', 'Amount', 'VWAP'}
        required = set()

        for field in market_fields:
            if field in self.formula:
                required.add(field)

        return required

    def _generate_class_name(self) -> str:
        """生成C++类名"""
        # 将因子名称转换为合适的类名
        class_name = self.factor_name.replace('_', '')
        # 首字母大写
        class_name = class_name[0].upper() + class_name[1:] if class_name else 'Factor'
        return f"Factor{class_name}"


class OptimizedFormulaParser:
    """优化的公式解析器 - 支持公共子表达式提取"""

    def __init__(self):
        self.expression_parser = ExpressionParser()
        self.cse_extractor = CommonSubexpressionExtractor()

    def parse_formula_with_optimization(self, formula: str) -> Tuple[List[str], str]:
        """
        解析公式并进行公共子表达式优化
        返回: (变量声明列表, 最终表达式)
        """
        try:
            # 解析为AST
            ast_root = self.expression_parser.parse(formula)

            # 提取公共子表达式
            common_subexprs, optimized_root = self.cse_extractor.extract_common_subexpressions(ast_root)

            # 生成C++代码
            variable_declarations = []
            for var_name, node in common_subexprs:
                cpp_expr = self._ast_to_cpp(node)
                variable_declarations.append(f"auto {var_name} = {cpp_expr};")

            final_expression = self._ast_to_cpp(optimized_root)

            return variable_declarations, final_expression
        except Exception as e:
            print(f"警告：公式优化失败，使用简单转换: {e}")
            # 如果优化失败，回退到简单转换
            simple_parser = FormulaParser()
            simple_result = simple_parser.parse_formula(formula)
            return [], simple_result

    def _ast_to_cpp(self, node: ASTNode) -> str:
        """将AST节点转换为C++代码"""
        if node.node_type == 'field':
            return f'get_field(data_map, "{node.value}")'
        elif node.node_type == 'constant':
            return node.value
        elif node.node_type == 'variable':
            return node.value
        elif node.node_type == 'operator':
            if not node.children:
                return f"feature_operators::{node.value}()"
            child_strs = [self._ast_to_cpp(child) for child in node.children]
            return f"feature_operators::{node.value}({','.join(child_strs)})"
        elif node.node_type == 'binary_op':
            if len(node.children) == 2:
                left = self._ast_to_cpp(node.children[0])
                right = self._ast_to_cpp(node.children[1])
                return f"({left}{node.value}{right})"
            else:
                return str(node.value)
        else:
            return str(node.value)


class FormulaParser:
    """传统公式解析器 - 将Python风格的公式转换为C++代码（兼容性）"""

    def __init__(self):
        # 操作符映射：Python -> C++
        self.operator_mapping = {
            'ts_Delay': 'feature_operators::ts_Delay',
            'ts_Mean': 'feature_operators::ts_Mean',
            'ts_Stdev': 'feature_operators::ts_Stdev',
            'ts_Delta': 'feature_operators::ts_Delta',
            'ts_Min': 'feature_operators::ts_Min',
            'ts_Max': 'feature_operators::ts_Max',
            'ts_Corr': 'feature_operators::ts_Corr',
            'ts_Sum': 'feature_operators::ts_Sum',
            'ts_Rank': 'feature_operators::ts_Rank',
            'ts_Scale': 'feature_operators::ts_Scale',
            'ts_Regression': 'feature_operators::ts_Regression',
            'pn_Rank': 'feature_operators::pn_Rank',
            'pn_Mean': 'feature_operators::pn_Mean',
            'pn_TransStd': 'feature_operators::pn_TransStd',
            'Tot_Mean': 'feature_operators::Tot_Mean',
            'Tot_Sum': 'feature_operators::Tot_Sum',
            'Tot_Stdev': 'feature_operators::Tot_Stdev',
            'Tot_Rank': 'feature_operators::Tot_Rank',
            'Tot_ArgMax': 'feature_operators::Tot_ArgMax',
            'Tot_ArgMin': 'feature_operators::Tot_ArgMin',
            'IfThen': 'feature_operators::IfThen',
            'Abs': 'feature_operators::Abs',
            'Log': 'feature_operators::Log',
            'Sqrt': 'feature_operators::Sqrt',
            'Equal': 'feature_operators::Equal',
            'getNan': 'feature_operators::getNan',
            'Power': 'feature_operators::Power',
        }

    def parse_formula(self, formula: str) -> str:
        """将Python公式转换为C++代码"""
        cpp_code = formula

        # 替换操作符
        for py_op, cpp_op in self.operator_mapping.items():
            cpp_code = cpp_code.replace(py_op, cpp_op)

        # 替换数据字段访问
        market_fields = ['Close', 'Open', 'High', 'Low', 'Volume', 'Amount', 'VWAP']
        for field in market_fields:
            cpp_code = cpp_code.replace(field, f'get_field(data_map, "{field}")')

        # 处理数值常量
        cpp_code = re.sub(r'\b(\d+\.?\d*)\b', r'\1', cpp_code)

        return cpp_code


class CppCodeGenerator:
    """C++代码生成器"""

    def __init__(self, output_dir: str, enable_optimization: bool = True):
        self.output_dir = Path(output_dir)
        self.enable_optimization = enable_optimization

        if enable_optimization:
            self.optimized_parser = OptimizedFormulaParser()
        self.formula_parser = FormulaParser()  # 保留作为备用

        # 创建输出目录
        self.output_dir.mkdir(parents=True, exist_ok=True)
        (self.output_dir / "include").mkdir(exist_ok=True)
        (self.output_dir / "src").mkdir(exist_ok=True)

    def generate_factor_header(self, factor: FactorInfo) -> str:
        """生成因子头文件"""
        template = f'''#ifndef GENERATED_FACTORS_{factor.class_name.upper()}_HPP
#define GENERATED_FACTORS_{factor.class_name.upper()}_HPP

#include "factor_framework/factor_base.hpp"
#include "feature_operators.hpp"

namespace generated_factors {{

/**
 * {factor.factor_name} 因子
 * ID: {factor.factor_id}
 * 公式: {factor.formula}
 */
class {factor.class_name} : public factor_framework::factor_base {{
public:
    /**
     * 构造函数
     */
    {factor.class_name}(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 计算因子值
     */
    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override;

    /**
     * 获取所需数据字段
     */
    std::vector<std::string> get_required_fields() const override;
}};
REGISTER_FACTOR_SIMPLE({factor.class_name}, {factor.factor_id}, "{factor.factor_name}", "{factor.formula}")

}} // namespace generated_factors

#endif // GENERATED_FACTORS_{factor.class_name.upper()}_HPP'''

        return template

    def generate_factor_implementation(self, factor: FactorInfo) -> str:
        """生成因子实现文件"""
        required_fields_str = ', '.join(f'"{field}"' for field in factor.required_fields)

        # 使用优化的公式解析器
        if self.enable_optimization:
            try:
                variable_declarations, final_expression = self.optimized_parser.parse_formula_with_optimization(factor.formula)

                # 生成优化的计算代码
                calculation_code = ""
                if variable_declarations:
                    calculation_code += "        // 公共子表达式\n"
                    for var_decl in variable_declarations:
                        calculation_code += f"        {var_decl}\n"
                    calculation_code += "\n        // 最终计算\n"
                    calculation_code += f"        auto result = {final_expression};"
                else:
                    calculation_code = f"        auto result = {final_expression};"

                optimization_comment = f"        // 优化信息：提取了 {len(variable_declarations)} 个公共子表达式\n"

            except Exception as e:
                print(f"警告：因子 {factor.factor_name} 优化失败，使用简单转换: {e}")
                cpp_formula = self.formula_parser.parse_formula(factor.formula)
                calculation_code = f"        auto result = {cpp_formula};"
                optimization_comment = "        // 注意：公式优化失败，使用简单转换\n"
        else:
            cpp_formula = self.formula_parser.parse_formula(factor.formula)
            calculation_code = f"        auto result = {cpp_formula};"
            optimization_comment = "        // 未启用优化\n"

        template = f'''#include "generated_factors/{factor.class_name.lower()}.hpp"
#include <stdexcept>

namespace generated_factors {{

{factor.class_name}::{factor.class_name}(int factor_id, const std::string& factor_name, const std::string& formula)
    : factor_base(factor_id, factor_name, formula) {{
}}

feature_operators::DataFrame {factor.class_name}::calculate(
    const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) {{

    // 验证输入数据
    if (!validate_input(data_map)) {{
        throw std::runtime_error("Invalid input data for factor " + get_name());
    }}

    try {{
{optimization_comment}        // 执行因子计算
{calculation_code}
        return result;
    }} catch (const std::exception& e) {{
        throw std::runtime_error("Error calculating factor " + get_name() + ": " + e.what());
    }}
}}

std::vector<std::string> {factor.class_name}::get_required_fields() const {{
    return {{{required_fields_str}}};
}}

// 简化的因子注册 - 注册到静态列表

}} // namespace generated_factors'''

        return template

    def generate_all_factors_header(self, factors: List[FactorInfo]) -> str:
        """生成包含所有因子的头文件"""
        includes = []
        for factor in factors:
            includes.append(f'#include "generated_factors/{factor.class_name.lower()}.hpp"')

        template = f'''#ifndef GENERATED_FACTORS_ALL_FACTORS_HPP
#define GENERATED_FACTORS_ALL_FACTORS_HPP

/**
 * 自动生成的因子头文件
 * 包含所有从feature.csv生成的因子类
 *
 * 生成的因子数量: {len(factors)}
 */

// 包含因子框架
#include "factor_framework/factor_framework.hpp"

// 包含所有生成的因子
{chr(10).join(includes)}

namespace generated_factors {{

/**
 * 获取所有生成的因子名称
 */
std::vector<std::string> get_all_generated_factor_names();

/**
 * 获取所有生成的因子ID
 */
std::vector<int> get_all_generated_factor_ids();

/**
 * 初始化所有生成的因子（确保注册）
 */
void initialize_all_factors();

/**
 * 批量注册所有生成的因子到factor_manager
 * @param manager 因子管理器
 * @return 成功注册的因子数量
 */
int register_all_factors_to_manager(std::shared_ptr<factor_framework::factor_manager> manager);

}} // namespace generated_factors

#endif // GENERATED_FACTORS_ALL_FACTORS_HPP'''

        return template

    def generate_all_factors_implementation(self, factors: List[FactorInfo]) -> str:
        """生成包含所有因子的实现文件"""
        factor_names = ', '.join(f'"{factor.factor_name}"' for factor in factors)
        factor_ids = ', '.join(str(factor.factor_id) for factor in factors)

        template = f'''#include "generated_factors/all_factors.hpp"
#include <iostream>
#include <algorithm>

namespace generated_factors {{

std::vector<std::string> get_all_generated_factor_names() {{
    return {{{factor_names}}};
}}

std::vector<int> get_all_generated_factor_ids() {{
    return {{{factor_ids}}};
}}

void initialize_all_factors() {{
    // 这个函数确保所有因子类被链接器包含
    static bool initialized = false;
    if (!initialized) {{
        std::cout << "✓ Generated factors library initialized" << std::endl;
        std::cout << "✓ Available factors: " << {len(factors)} << std::endl;

        // 显示所有可用的因子
        auto factor_names = get_all_generated_factor_names();
        for (const auto& factor_name : factor_names) {{
            std::cout << "  - " << factor_name << std::endl;
        }}

        initialized = true;
    }}
}}

/**
 * 批量注册所有生成的因子到factor_manager
 * @param manager 因子管理器
 * @return 成功注册的因子数量
 */
int register_all_factors_to_manager(std::shared_ptr<factor_framework::factor_manager> manager) {{
    if (!manager) {{
        std::cerr << "Error: factor_manager is null" << std::endl;
        return 0;
    }}

    // 从静态注册列表创建所有因子实例
    auto& registration_list = factor_framework::get_factor_registration_list();

    int registered_count = 0;
    for (auto& creator : registration_list) {{
        try {{
            auto factor = creator();
            if (factor && manager->register_factor(std::move(factor))) {{
                registered_count++;
            }}
        }} catch (const std::exception& e) {{
            std::cerr << "Error creating factor: " << e.what() << std::endl;
        }}
    }}

    std::cout << "✓ Registered " << registered_count << " generated factors to manager" << std::endl;
    return registered_count;
}}

}} // namespace generated_factors'''

        return template

    def generate_cmake_file(self, factors: List[FactorInfo]) -> str:
        """生成CMake构建文件"""
        source_files = []
        for factor in factors:
            source_files.append(f"src/{factor.class_name.lower()}.cpp")

        template = f'''# 自动生成的因子CMake文件
cmake_minimum_required(VERSION 3.10)

# 添加生成的因子源文件
set(GENERATED_FACTOR_SOURCES
    src/all_factors.cpp
{chr(10).join(f"    {src}" for src in source_files)}
)

# 添加生成的因子头文件目录
set(GENERATED_FACTOR_INCLUDE_DIRS
    include
)

# 创建生成因子库
add_library(generated_factors STATIC ${{GENERATED_FACTOR_SOURCES}})

# 设置包含目录
target_include_directories(generated_factors PUBLIC
    ${{GENERATED_FACTOR_INCLUDE_DIRS}}
    ${{CMAKE_SOURCE_DIR}}/include
)

# 链接feature_operators库
target_link_libraries(generated_factors
    feature_ops_lib
)

# 设置编译选项
target_compile_features(generated_factors PUBLIC cxx_std_17)
target_compile_options(generated_factors PRIVATE
    $<$<CXX_COMPILER_ID:GNU>:-Wall -Wextra -O3>
    $<$<CXX_COMPILER_ID:Clang>:-Wall -Wextra -O3>
    $<$<CXX_COMPILER_ID:MSVC>:/W4 /O2>
)

# 安装规则
install(TARGETS generated_factors
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
)

install(DIRECTORY include/
    DESTINATION include
    FILES_MATCHING PATTERN "*.hpp"
)'''

        return template

    def generate_factors(self, factors: List[FactorInfo]):
        """生成所有因子代码"""
        print(f"开始生成 {len(factors)} 个因子的C++代码...")

        # 生成各个因子的头文件和实现文件
        for i, factor in enumerate(factors):
            print(f"生成因子 {i+1}/{len(factors)}: {factor.factor_name}")

            # 生成头文件
            header_content = self.generate_factor_header(factor)
            header_file = self.output_dir / "include" / "generated_factors" / f"{factor.class_name.lower()}.hpp"
            header_file.parent.mkdir(parents=True, exist_ok=True)
            header_file.write_text(header_content, encoding='utf-8')

            # 生成实现文件
            impl_content = self.generate_factor_implementation(factor)
            impl_file = self.output_dir / "src" / f"{factor.class_name.lower()}.cpp"
            impl_file.write_text(impl_content, encoding='utf-8')

        # 生成总头文件
        print("生成总头文件...")
        all_header_content = self.generate_all_factors_header(factors)
        all_header_file = self.output_dir / "include" / "generated_factors" / "all_factors.hpp"
        all_header_file.write_text(all_header_content, encoding='utf-8')

        # 生成总实现文件
        print("生成总实现文件...")
        all_impl_content = self.generate_all_factors_implementation(factors)
        all_impl_file = self.output_dir / "src" / "all_factors.cpp"
        all_impl_file.write_text(all_impl_content, encoding='utf-8')

        # 生成CMake文件
        print("生成CMake文件...")
        cmake_content = self.generate_cmake_file(factors)
        cmake_file = self.output_dir / "CMakeLists.txt"
        cmake_file.write_text(cmake_content, encoding='utf-8')

        # 生成因子信息JSON文件
        print("生成因子信息文件...")
        self.generate_factor_info_json(factors)

        print(f"代码生成完成！输出目录: {self.output_dir}")

    def generate_factor_info_json(self, factors: List[FactorInfo]):
        """生成因子信息JSON文件"""
        factor_info = []
        for factor in factors:
            factor_info.append({
                "id": factor.factor_id,
                "name": factor.factor_name,
                "formula": factor.formula,
                "class_name": factor.class_name,
                "required_fields": list(factor.required_fields)
            })

        info_file = self.output_dir / "factor_info.json"
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(factor_info, f, indent=2, ensure_ascii=False)


class FactorCSVParser:
    """因子CSV文件解析器"""

    def __init__(self, csv_file_path: str):
        self.csv_file_path = csv_file_path

    def parse(self) -> List[FactorInfo]:
        """解析CSV文件，返回因子信息列表"""
        factors = []

        try:
            with open(self.csv_file_path, 'r', encoding='utf-8') as f:
                # 跳过第一行（标题行）
                next(f)

                for line_num, line in enumerate(f, start=2):
                    line = line.strip()
                    if not line:
                        continue

                    try:
                        factor = self._parse_line(line)
                        if factor:
                            factors.append(factor)
                    except Exception as e:
                        print(f"警告：解析第{line_num}行时出错: {e}")
                        continue

        except FileNotFoundError:
            raise FileNotFoundError(f"找不到CSV文件: {self.csv_file_path}")
        except Exception as e:
            raise RuntimeError(f"解析CSV文件时出错: {e}")

        print(f"成功解析 {len(factors)} 个因子")
        return factors

    def _parse_line(self, line: str) -> FactorInfo:
        """解析CSV行"""
        # 简单的CSV解析（处理带引号的字段）
        parts = []
        current_part = ""
        in_quotes = False

        for char in line:
            if char == '"':
                in_quotes = not in_quotes
            elif char == ',' and not in_quotes:
                parts.append(current_part.strip())
                current_part = ""
            else:
                current_part += char

        if current_part:
            parts.append(current_part.strip())

        if len(parts) < 3:
            raise ValueError(f"CSV行格式错误，期望至少3列: {line}")

        # 解析字段
        factor_id = int(parts[0]) if parts[0].isdigit() else 0
        factor_name = parts[1].strip('"')
        formula = parts[2].strip('"')

        return FactorInfo(factor_id, factor_name, formula)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="因子代码自动生成器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python factor_code_generator.py --input feature.csv --output_dir generated_factors
  python factor_code_generator.py -i feature.csv -o generated_factors --verbose
  python factor_code_generator.py --input feature.csv --output_dir generated_factors --filter "p1_corrs*"
        """
    )

    parser.add_argument(
        '--input', '-i',
        required=True,
        help='输入的因子CSV文件路径'
    )

    parser.add_argument(
        '--output_dir', '-o',
        default='generated_factors',
        help='输出目录路径（默认: generated_factors）'
    )

    parser.add_argument(
        '--filter', '-f',
        help='因子名称过滤器（支持通配符，如 "p1_*"）'
    )

    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='启用详细输出'
    )

    parser.add_argument(
        '--dry_run',
        action='store_true',
        help='只解析不生成代码（用于验证）'
    )

    parser.add_argument(
        '--max_factors',
        type=int,
        help='限制生成的因子数量（用于测试）'
    )

    parser.add_argument(
        '--enable_optimization',
        action='store_true',
        default=True,
        help='启用公共子表达式优化（默认启用）'
    )

    parser.add_argument(
        '--disable_optimization',
        action='store_true',
        help='禁用公共子表达式优化'
    )

    args = parser.parse_args()

    try:
        # 解析CSV文件
        print(f"解析因子配置文件: {args.input}")
        csv_parser = FactorCSVParser(args.input)
        factors = csv_parser.parse()

        if not factors:
            print("错误：没有找到有效的因子定义")
            return 1

        # 应用过滤器
        if args.filter:
            import fnmatch
            filtered_factors = []
            for factor in factors:
                if fnmatch.fnmatch(factor.factor_name, args.filter):
                    filtered_factors.append(factor)
            factors = filtered_factors
            print(f"应用过滤器 '{args.filter}' 后，剩余 {len(factors)} 个因子")

        # 限制因子数量
        if args.max_factors and len(factors) > args.max_factors:
            factors = factors[:args.max_factors]
            print(f"限制因子数量为 {args.max_factors}")

        # 打印因子信息
        if args.verbose:
            print("\n因子列表:")
            for factor in factors:
                print(f"  ID: {factor.factor_id}, 名称: {factor.factor_name}")
                print(f"    公式: {factor.formula}")
                print(f"    所需字段: {', '.join(factor.required_fields)}")
                print(f"    类名: {factor.class_name}")
                print()

        if args.dry_run:
            print(f"干运行模式：解析了 {len(factors)} 个因子，未生成代码")
            return 0

        # 确定是否启用优化
        enable_optimization = args.enable_optimization and not args.disable_optimization
        optimization_status = "启用" if enable_optimization else "禁用"
        print(f"公共子表达式优化: {optimization_status}")

        # 生成代码
        print(f"\n开始生成代码到目录: {args.output_dir}")
        code_generator = CppCodeGenerator(args.output_dir, enable_optimization=enable_optimization)
        code_generator.generate_factors(factors)

        # 生成使用说明
        generate_usage_instructions(args.output_dir, factors)

        print("\n✅ 代码生成完成！")
        print(f"📁 输出目录: {args.output_dir}")
        print(f"📊 生成因子数量: {len(factors)}")
        print(f"📄 生成文件数量: {len(factors) * 2 + 4}")  # 每个因子2个文件 + 4个公共文件

        return 0

    except Exception as e:
        print(f"❌ 错误: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1


def generate_usage_instructions(output_dir: str, factors: List[FactorInfo]):
    """生成使用说明文件"""
    instructions = f"""# 生成的因子代码使用说明

## 概述
本目录包含从 feature.csv 自动生成的 {len(factors)} 个因子的C++代码。

## 目录结构
```
{output_dir}/
├── include/
│   └── generated_factors/
│       ├── all_factors.hpp          # 总头文件
│       ├── factor*.hpp              # 各因子头文件
├── src/
│   ├── all_factors.cpp              # 总实现文件
│   ├── factor*.cpp                  # 各因子实现文件
├── CMakeLists.txt                   # CMake构建文件
├── factor_info.json                 # 因子信息JSON文件
└── README.md                        # 本文件

## 编译方法

### 1. 使用CMake
```bash
mkdir build
cd build
cmake ..
make
```

### 2. 集成到现有项目
将以下内容添加到您的CMakeLists.txt中：
```cmake
add_subdirectory({output_dir})
target_link_libraries(your_target generated_factors)
```

## 使用方法

### 1. 包含头文件
```cpp
#include "generated_factors/all_factors.hpp"
```

### 2. 初始化因子系统
```cpp
// 初始化框架
FACTOR_FRAMEWORK_INIT();

// 初始化生成的因子
generated_factors::initializeAllFactors();
```

### 3. 创建因子管理器和数据接口
```cpp
auto factor_manager = std::make_shared<factor_framework::FactorManager>();
auto data_interface = std::make_shared<factor_framework::DataInterface>();

// 加载因子配置
factor_manager->loadFactorsFromCSV("feature.csv");

// 加载数据
data_interface->loadDataFromCSV("Close", "close.csv");
data_interface->loadDataFromCSV("Volume", "volume.csv");
// ... 加载其他必要的数据字段
```

### 4. 创建计算引擎并执行计算
```cpp
factor_framework::FactorEngine engine(factor_manager, data_interface);

// 选择要计算的因子
std::vector<int> factor_ids = {{0, 1, 2, 3, 4}};  // 选择前5个因子
factor_manager->selectFactors(factor_ids);

// 执行计算
auto result = engine.calculateSelectedFactors();

if (result.success) {{
    for (const auto& [factor_name, factor_data] : result.factor_results) {{
        std::cout << "因子: " << factor_name << std::endl;
        // 处理计算结果...
    }}
}} else {{
    std::cerr << "计算失败: " << result.error_message << std::endl;
}}
```

## 生成的因子列表
{chr(10).join(f"- {factor.factor_id}: {factor.factor_name} - {factor.formula}" for factor in factors[:10])}
{"..." if len(factors) > 10 else ""}

总计: {len(factors)} 个因子

## 注意事项
1. 确保所有必要的数据字段都已加载
2. 数据维度必须一致
3. 建议在生产环境中启用性能统计来监控计算性能
4. 大量因子计算时建议使用多线程

## 性能优化建议
1. 使用多线程计算：`engine.setNumThreads(8)`
2. 启用性能统计：`engine.enablePerformanceStats(true)`
3. 预热计算引擎：`engine.warmUp()`
4. 批量计算相关因子以提高缓存效率

## 故障排除
如果遇到编译或运行问题，请检查：
1. feature_operators库是否正确安装
2. 所有依赖的数据字段是否已加载
3. 数据格式是否正确
4. 内存是否充足

更多信息请参考factor_framework文档。
"""

    readme_file = Path(output_dir) / "README.md"
    readme_file.write_text(instructions, encoding='utf-8')


if __name__ == "__main__":
    sys.exit(main())