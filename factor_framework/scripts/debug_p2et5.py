#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试p2_et5因子的优化问题
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from optimized_factor_code_generator import (
    OptimizedFormulaParser,
    FactorInfo
)

def debug_p2et5():
    """调试p2_et5因子"""
    formula = "ts_Stdev(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Stdev(Close/ts_Delay(Close,1)-1,60))"
    
    print(f"调试公式: {formula}")
    
    # 创建优化解析器
    parser = OptimizedFormulaParser()
    
    try:
        # 解析公式
        variable_declarations, final_expression = parser.parse_formula_with_optimization(formula)
        
        print(f"\n解析结果:")
        print(f"变量声明数量: {len(variable_declarations)}")
        for i, var_decl in enumerate(variable_declarations):
            print(f"  {i+1}. {var_decl}")
        
        print(f"\n最终表达式: {final_expression}")
        
        # 生成完整的C++代码
        print(f"\n生成的C++代码:")
        if variable_declarations:
            print("        // 公共子表达式")
            for var_decl in variable_declarations:
                print(f"        {var_decl}")
            print("\n        // 最终计算")
            print(f"        auto result = {final_expression};")
        else:
            print(f"        auto result = {final_expression};")
        
        # 创建FactorInfo并测试代码生成器
        print(f"\n测试代码生成器:")
        factor = FactorInfo(21, "p2_et5", formula)
        
        from optimized_factor_code_generator import CppCodeGenerator
        generator = CppCodeGenerator("/tmp/test_output", enable_optimization=True)
        
        # 生成实现代码
        impl_code = generator.generate_factor_implementation(factor)
        print("生成的实现代码:")
        print(impl_code)
        
    except Exception as e:
        print(f"调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_p2et5()
