#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试p2_et16因子的解析问题
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from optimized_factor_code_generator import (
    ExpressionParser,
    OptimizedFormulaParser
)

def debug_p2et16():
    """调试p2_et16因子"""
    formula = "Tot_Mean(IfThen(-(Close/ts_Delay(Close,1)-1)+(Tot_Mean(Close/ts_Delay(Close,1)-1)-Tot_Stdev(Close/ts_Delay(Close,1)-1)),Close/ts_Delay(Close,1)-1,getNan(Close)))"
    
    print(f"调试公式: {formula}")
    print(f"公式长度: {len(formula)}")
    
    # 测试tokenizer
    parser = ExpressionParser()
    tokens = parser._tokenize(formula)
    print(f"\nTokens ({len(tokens)}):")
    for i, token in enumerate(tokens):
        print(f"  {i:2d}: '{token}'")
    
    # 测试解析
    try:
        ast_node = parser.parse(formula)
        print(f"\n解析成功!")
        print(f"根节点: {ast_node.node_type} - {ast_node.value}")
        print(f"转换为C++: {ast_node.to_string()}")
        
        # 递归打印AST结构
        def print_ast(node, indent=0):
            prefix = "  " * indent
            print(f"{prefix}节点: {node.node_type} - {node.value}")
            for child in node.children:
                print_ast(child, indent + 1)
        
        print("\nAST结构:")
        print_ast(ast_node)
        
    except Exception as e:
        print(f"\n解析失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 测试优化解析器
    print(f"\n=== 测试优化解析器 ===")
    try:
        opt_parser = OptimizedFormulaParser()
        variable_declarations, final_expression = opt_parser.parse_formula_with_optimization(formula)
        
        print(f"变量声明数量: {len(variable_declarations)}")
        for i, var_decl in enumerate(variable_declarations):
            print(f"  {i+1}. {var_decl}")
        
        print(f"\n最终表达式: {final_expression}")
        
    except Exception as e:
        print(f"优化解析失败: {e}")
        import traceback
        traceback.print_exc()

def test_simpler_cases():
    """测试更简单的情况"""
    print(f"\n=== 测试简单情况 ===")
    
    parser = ExpressionParser()
    
    test_cases = [
        "Close",
        "ts_Delay(Close,1)",
        "Close/ts_Delay(Close,1)-1",
        "-(Close/ts_Delay(Close,1)-1)",
        "getNan(Close)",
        "IfThen(Close,Open,High)",
        "Tot_Mean(Close)",
        "Tot_Mean(IfThen(Close,Open,High))"
    ]
    
    for formula in test_cases:
        print(f"\n测试: {formula}")
        try:
            ast_node = parser.parse(formula)
            print(f"  成功: {ast_node.to_string()}")
        except Exception as e:
            print(f"  失败: {e}")

if __name__ == "__main__":
    test_simpler_cases()
    debug_p2et16()
