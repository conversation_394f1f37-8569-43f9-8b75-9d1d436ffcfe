#ifndef GENERATED_FACTORS_FACTORP2ET8_HPP
#define GENERATED_FACTORS_FACTORP2ET8_HPP

#include "factor_framework/factor_base.hpp"
#include "feature_operators.hpp"

namespace generated_factors {

/**
 * p2_et8 因子
 * ID: 24
 * 公式: Abs(ts_Mean(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Mean(Close/ts_Delay(Close,1)-1,60)))
 */
class FactorP2et8 : public factor_framework::factor_base {
public:
    /**
     * 构造函数
     */
    FactorP2et8(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 计算因子值
     */
    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override;

    /**
     * 获取所需数据字段
     */
    std::vector<std::string> get_required_fields() const override;
};
REGISTER_FACTOR_SIMPLE(FactorP2et8, 24, "p2_et8", "Abs(ts_Mean(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Mean(Close/ts_Delay(Close,1)-1,60)))")

} // namespace generated_factors

#endif // GENERATED_FACTORS_FACTORP2ET8_HPP