#ifndef GENERATED_FACTORS_ALL_FACTORS_HPP
#define GENERATED_FACTORS_ALL_FACTORS_HPP

/**
 * 自动生成的因子头文件
 * 包含所有从feature.csv生成的因子类
 *
 * 生成的因子数量: 30
 */

// 包含因子框架
#include "factor_framework/factor_framework.hpp"

// 包含所有生成的因子
#include "generated_factors/factorp1corrs0.hpp"
#include "generated_factors/factorp1corrs1.hpp"
#include "generated_factors/factorp1corrs2.hpp"
#include "generated_factors/factorp1corrs3.hpp"
#include "generated_factors/factorp1corrs4.hpp"
#include "generated_factors/factorp1corrs5.hpp"
#include "generated_factors/factorp1corrs6.hpp"
#include "generated_factors/factorp1corrs7.hpp"
#include "generated_factors/factorp1corrs8.hpp"
#include "generated_factors/factorp1corrs9.hpp"
#include "generated_factors/factorp1corrs10.hpp"
#include "generated_factors/factorp1corrs11.hpp"
#include "generated_factors/factorp1corrs12.hpp"
#include "generated_factors/factorp1corrs13.hpp"
#include "generated_factors/factorp1corrs14.hpp"
#include "generated_factors/factorp1corrs15.hpp"
#include "generated_factors/factorp2et0.hpp"
#include "generated_factors/factorp2et1.hpp"
#include "generated_factors/factorp2et2.hpp"
#include "generated_factors/factorp2et3.hpp"
#include "generated_factors/factorp2et4.hpp"
#include "generated_factors/factorp2et5.hpp"
#include "generated_factors/factorp2et6.hpp"
#include "generated_factors/factorp2et7.hpp"
#include "generated_factors/factorp2et8.hpp"
#include "generated_factors/factorp2et9.hpp"
#include "generated_factors/factorp2et10.hpp"
#include "generated_factors/factorp2et11.hpp"
#include "generated_factors/factorp2et12.hpp"
#include "generated_factors/factorp2et13.hpp"

namespace generated_factors {

/**
 * 获取所有生成的因子名称
 */
std::vector<std::string> get_all_generated_factor_names();

/**
 * 获取所有生成的因子ID
 */
std::vector<int> get_all_generated_factor_ids();

/**
 * 初始化所有生成的因子（确保注册）
 */
void initialize_all_factors();

/**
 * 批量注册所有生成的因子到factor_manager
 * @param manager 因子管理器
 * @return 成功注册的因子数量
 */
int register_all_factors_to_manager(std::shared_ptr<factor_framework::factor_manager> manager);

} // namespace generated_factors

#endif // GENERATED_FACTORS_ALL_FACTORS_HPP