#ifndef GENERATED_FACTORS_FACTORP2ET1_HPP
#define GENERATED_FACTORS_FACTORP2ET1_HPP

#include "factor_framework/factor_base.hpp"
#include "feature_operators.hpp"

namespace generated_factors {

/**
 * p2_et1 因子
 * ID: 17
 * 公式: Tot_Mean(IfThen(ts_Sum(IfThen(Tot_Rank(Volume-ts_Delay(Volume,1))-0.9,1,0),10),Close/ts_Delay(Close,1)-1,getNan(Close/ts_Delay(Close,1)-1)))
 */
class FactorP2et1 : public factor_framework::factor_base {
public:
    /**
     * 构造函数
     */
    FactorP2et1(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 计算因子值
     */
    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override;

    /**
     * 获取所需数据字段
     */
    std::vector<std::string> get_required_fields() const override;
};
REGISTER_FACTOR_SIMPLE(FactorP2et1, 17, "p2_et1", "Tot_Mean(IfThen(ts_Sum(IfThen(Tot_Rank(Volume-ts_Delay(Volume,1))-0.9,1,0),10),Close/ts_Delay(Close,1)-1,getNan(Close/ts_Delay(Close,1)-1)))")

} // namespace generated_factors

#endif // GENERATED_FACTORS_FACTORP2ET1_HPP