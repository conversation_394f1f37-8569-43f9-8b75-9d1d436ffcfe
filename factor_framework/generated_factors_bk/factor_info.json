[{"id": 0, "name": "p1_corrs0", "formula": "ts_Corr(Close,Volume,60)", "class_name": "FactorP1corrs0", "required_fields": ["Close", "Volume"]}, {"id": 1, "name": "p1_corrs1", "formula": "ts_<PERSON>rr(Close/ts_Delay(Close,1)-1,Volume,60)", "class_name": "FactorP1corrs1", "required_fields": ["Close", "Volume"]}, {"id": 2, "name": "p1_corrs2", "formula": "ts_<PERSON>rr(ts_<PERSON>ay(Close,1),Volume,60)", "class_name": "FactorP1corrs2", "required_fields": ["Close", "Volume"]}, {"id": 3, "name": "p1_corrs3", "formula": "ts_<PERSON><PERSON>(Close,ts_Delay(Volume,1),60)", "class_name": "FactorP1corrs3", "required_fields": ["Close", "Volume"]}, {"id": 4, "name": "p1_corrs4", "formula": "ts_<PERSON><PERSON>(ts_Delay(Close/ts_Delay(Close,1)-1,1),Close/ts_Delay(Close,1)-1,60)", "class_name": "FactorP1corrs4", "required_fields": ["Close"]}, {"id": 5, "name": "p1_corrs5", "formula": "ts_<PERSON><PERSON>(ts_Delay(Close/ts_Delay(Close,1)-1,1),Close,60)", "class_name": "FactorP1corrs5", "required_fields": ["Close"]}, {"id": 6, "name": "p1_corrs6", "formula": "ts_<PERSON><PERSON>(Volume,Volume-ts_Delay(Volume,1),60)", "class_name": "FactorP1corrs6", "required_fields": ["Volume"]}, {"id": 7, "name": "p1_corrs7", "formula": "ts_<PERSON><PERSON>(Volume-ts_Delay(Volume,1), Volume-ts_Delay(Volume,1) - ts_Delay(Volume-ts_Delay(Volume,1),1),60)", "class_name": "FactorP1corrs7", "required_fields": ["Volume"]}, {"id": 8, "name": "p1_corrs8", "formula": "ts_Corr(VWAP,Volume,60)", "class_name": "FactorP1corrs8", "required_fields": ["Volume", "VWAP"]}, {"id": 9, "name": "p1_corrs9", "formula": "ts_Corr(VWAP/ts_Delay(VWAP,1)-1,Volume,60)", "class_name": "FactorP1corrs9", "required_fields": ["Volume", "VWAP"]}, {"id": 10, "name": "p1_corrs10", "formula": "ts_Corr(ts_Delay(VWAP,1),Volume,60)", "class_name": "FactorP1corrs10", "required_fields": ["Volume", "VWAP"]}, {"id": 11, "name": "p1_corrs11", "formula": "ts_<PERSON>rr(VWAP,ts_Delay(Volume,1),60)", "class_name": "FactorP1corrs11", "required_fields": ["Volume", "VWAP"]}, {"id": 12, "name": "p1_corrs12", "formula": "ts_<PERSON>rr(ts_Delay(VWAP/ts_Delay(VWAP,1)-1,1),VWAP/ts_Delay(VWAP,1)-1,60)", "class_name": "FactorP1corrs12", "required_fields": ["VWAP"]}, {"id": 13, "name": "p1_corrs13", "formula": "ts_<PERSON>rr(ts_Delay(VWAP/ts_Delay(VWAP,1)-1,1),VWA<PERSON>,60)", "class_name": "FactorP1corrs13", "required_fields": ["VWAP"]}, {"id": 14, "name": "p1_corrs14", "formula": "ts_<PERSON><PERSON>(Volume,Volume-ts_Delay(Volume,1),60)", "class_name": "FactorP1corrs14", "required_fields": ["Volume"]}, {"id": 15, "name": "p1_corrs15", "formula": "ts_<PERSON><PERSON>(Volume-ts_Delay(Volume,1), Volume-ts_Delay(Volume,1) - ts_Delay(Volume-ts_Delay(Volume,1),1),60)", "class_name": "FactorP1corrs15", "required_fields": ["Volume"]}, {"id": 16, "name": "p2_et0", "formula": "Tot_Mean(IfThen(IfThen(Volume-ts_Delay(Volume,1)-Tot_<PERSON>(Volume-ts_Delay(Volume,1))-Tot_Stdev(Volume-ts_Delay(Volume,1)),1,0),Close/ts_Delay(Close,1)-1,get<PERSON><PERSON>(Close/ts_Delay(Close,1)-1)))", "class_name": "FactorP2et0", "required_fields": ["Close", "Volume"]}, {"id": 17, "name": "p2_et1", "formula": "Tot_Mean(IfThen(ts_Sum(IfThen(Tot_Rank(Volume-ts_Delay(Volume,1))-0.9,1,0),10),Close/ts_Delay(Close,1)-1,get<PERSON><PERSON>(Close/ts_Delay(Close,1)-1)))", "class_name": "FactorP2et1", "required_fields": ["Close", "Volume"]}, {"id": 18, "name": "p2_et2", "formula": "Tot_Stdev(IfThen(ts_Sum(IfThen(Tot_Rank(Close/ts_Delay(Close,1)-1)-0.9,1,0),10),Close/ts_Delay(Close,1)-1,get<PERSON><PERSON>(Close/ts_Delay(Close,1)-1)))", "class_name": "FactorP2et2", "required_fields": ["Close"]}, {"id": 19, "name": "p2_et3", "formula": "Tot_<PERSON>(IfThen(IfThen(Close/ts_Delay(Close,1)-1-<PERSON><PERSON>_<PERSON>(Close/ts_Delay(Close,1)-1)-To<PERSON>_Stdev(Close/ts_Delay(Close,1)-1),1,0),Close/ts_Delay(Close,1)-1,get<PERSON><PERSON>(Close/ts_Delay(Close,1)-1)))", "class_name": "FactorP2et3", "required_fields": ["Close"]}, {"id": 20, "name": "p2_et4", "formula": "Tot_<PERSON>(IfThen(ts_Sum(IfThen(Tot_Rank(Close/ts_Delay(Close,1)-1)-0.9,1,0),10),Close/ts_Delay(Close,1)-1,get<PERSON><PERSON>(Close/ts_Delay(Close,1)-1)))", "class_name": "FactorP2et4", "required_fields": ["Close"]}, {"id": 21, "name": "p2_et5", "formula": "ts_Stdev(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Stdev(Close/ts_Delay(Close,1)-1,60))", "class_name": "FactorP2et5", "required_fields": ["Close"]}, {"id": 22, "name": "p2_et6", "formula": "Abs(ts_Stdev(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Stdev(Close/ts_Delay(Close,1)-1,60)))", "class_name": "FactorP2et6", "required_fields": ["Close"]}, {"id": 23, "name": "p2_et7", "formula": "ts_Mean(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Mean(Close/ts_Delay(Close,1)-1,60))", "class_name": "FactorP2et7", "required_fields": ["Close"]}, {"id": 24, "name": "p2_et8", "formula": "Abs(ts_Mean(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Mean(Close/ts_Delay(Close,1)-1,60)))", "class_name": "FactorP2et8", "required_fields": ["Close"]}, {"id": 25, "name": "p2_et9", "formula": "ts_Mean((Close/ts_Delay(Close,1)-1)*IfThen(Volume-ts_Mean(Volume,30)-1.210*ts_Stdev(Volume,30),1,0),30)", "class_name": "FactorP2et9", "required_fields": ["Close", "Volume"]}, {"id": 26, "name": "p2_et10", "formula": "ts_Mean(IfThen((Close/ts_Delay(Close,1)-1),(Close/ts_Delay(Close,1)-1),0)*IfThen(Volume-ts_Mean(Volume,30)-0.10*ts_Stdev(Volume,30),1,0),30)", "class_name": "FactorP2et10", "required_fields": ["Close", "Volume"]}, {"id": 27, "name": "p2_et11", "formula": "To<PERSON>_<PERSON><PERSON>(IfThen(ts_Delay(ts_<PERSON>(Low,30),1)-Low,1,0))", "class_name": "FactorP2et11", "required_fields": ["Low"]}, {"id": 28, "name": "p2_et12", "formula": "Tot_Stdev(Abs(Close/ts_Delay(Close,1)-1-pn_Mean(Close/ts_Delay(Close,1)-1)))", "class_name": "FactorP2et12", "required_fields": ["Close"]}, {"id": 29, "name": "p2_et13", "formula": "Tot_Stdev(pn_Rank(Close/ts_Delay(Close,1)-1))", "class_name": "FactorP2et13", "required_fields": ["Close"]}]