#include "generated_factors/factorp2et13.hpp"
#include <stdexcept>

namespace generated_factors {

FactorP2et13::FactorP2et13(int factor_id, const std::string& factor_name, const std::string& formula)
    : factor_base(factor_id, factor_name, formula) {
}

feature_operators::DataFrame FactorP2et13::calculate(
    const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) {

    // 验证输入数据
    if (!validate_input(data_map)) {
        throw std::runtime_error("Invalid input data for factor " + get_name());
    }

    try {
        // 执行因子计算
        auto result = feature_operators::Tot_Stdev(feature_operators::pn_Rank(get_field(data_map, "Close")/feature_operators::ts_Delay(get_field(data_map, "Close"),1)-1));
        return result;
    } catch (const std::exception& e) {
        throw std::runtime_error("Error calculating factor " + get_name() + ": " + e.what());
    }
}

std::vector<std::string> FactorP2et13::get_required_fields() const {
    return {"Close"};
}

// 简化的因子注册 - 注册到静态列表

} // namespace generated_factors