#include "generated_factors/factorp2et11.hpp"
#include <stdexcept>

namespace generated_factors {

FactorP2et11::FactorP2et11(int factor_id, const std::string& factor_name, const std::string& formula)
    : factor_base(factor_id, factor_name, formula) {
}

feature_operators::DataFrame FactorP2et11::calculate(
    const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) {

    // 验证输入数据
    if (!validate_input(data_map)) {
        throw std::runtime_error("Invalid input data for factor " + get_name());
    }

    try {
        // 执行因子计算
        auto result = feature_operators::Tot_Sum(feature_operators::IfThen(feature_operators::ts_Delay(feature_operators::ts_Min(get_field(data_map, "Low"),30),1)-get_field(data_map, "Low"),1,0));
        return result;
    } catch (const std::exception& e) {
        throw std::runtime_error("Error calculating factor " + get_name() + ": " + e.what());
    }
}

std::vector<std::string> FactorP2et11::get_required_fields() const {
    return {"Low"};
}

// 简化的因子注册 - 注册到静态列表

} // namespace generated_factors