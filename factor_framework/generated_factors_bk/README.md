# 生成的因子代码使用说明

## 概述
本目录包含从 feature.csv 自动生成的 30 个因子的C++代码。

## 目录结构
```
../generated_factors/
├── include/
│   └── generated_factors/
│       ├── all_factors.hpp          # 总头文件
│       ├── factor*.hpp              # 各因子头文件
├── src/
│   ├── all_factors.cpp              # 总实现文件
│   ├── factor*.cpp                  # 各因子实现文件
├── CMakeLists.txt                   # CMake构建文件
├── factor_info.json                 # 因子信息JSON文件
└── README.md                        # 本文件

## 编译方法

### 1. 使用CMake
```bash
mkdir build
cd build
cmake ..
make
```

### 2. 集成到现有项目
将以下内容添加到您的CMakeLists.txt中：
```cmake
add_subdirectory(../generated_factors)
target_link_libraries(your_target generated_factors)
```

## 使用方法

### 1. 包含头文件
```cpp
#include "generated_factors/all_factors.hpp"
```

### 2. 初始化因子系统
```cpp
// 初始化框架
FACTOR_FRAMEWORK_INIT();

// 初始化生成的因子
generated_factors::initializeAllFactors();
```

### 3. 创建因子管理器和数据接口
```cpp
auto factor_manager = std::make_shared<factor_framework::FactorManager>();
auto data_interface = std::make_shared<factor_framework::DataInterface>();

// 加载因子配置
factor_manager->loadFactorsFromCSV("feature.csv");

// 加载数据
data_interface->loadDataFromCSV("Close", "close.csv");
data_interface->loadDataFromCSV("Volume", "volume.csv");
// ... 加载其他必要的数据字段
```

### 4. 创建计算引擎并执行计算
```cpp
factor_framework::FactorEngine engine(factor_manager, data_interface);

// 选择要计算的因子
std::vector<int> factor_ids = {0, 1, 2, 3, 4};  // 选择前5个因子
factor_manager->selectFactors(factor_ids);

// 执行计算
auto result = engine.calculateSelectedFactors();

if (result.success) {
    for (const auto& [factor_name, factor_data] : result.factor_results) {
        std::cout << "因子: " << factor_name << std::endl;
        // 处理计算结果...
    }
} else {
    std::cerr << "计算失败: " << result.error_message << std::endl;
}
```

## 生成的因子列表
- 0: p1_corrs0 - ts_Corr(Close,Volume,60)
- 1: p1_corrs1 - ts_Corr(Close/ts_Delay(Close,1)-1,Volume,60)
- 2: p1_corrs2 - ts_Corr(ts_Delay(Close,1),Volume,60)
- 3: p1_corrs3 - ts_Corr(Close,ts_Delay(Volume,1),60)
- 4: p1_corrs4 - ts_Corr(ts_Delay(Close/ts_Delay(Close,1)-1,1),Close/ts_Delay(Close,1)-1,60)
- 5: p1_corrs5 - ts_Corr(ts_Delay(Close/ts_Delay(Close,1)-1,1),Close,60)
- 6: p1_corrs6 - ts_Corr(Volume,Volume-ts_Delay(Volume,1),60)
- 7: p1_corrs7 - ts_Corr(Volume-ts_Delay(Volume,1), Volume-ts_Delay(Volume,1) - ts_Delay(Volume-ts_Delay(Volume,1),1),60)
- 8: p1_corrs8 - ts_Corr(VWAP,Volume,60)
- 9: p1_corrs9 - ts_Corr(VWAP/ts_Delay(VWAP,1)-1,Volume,60)
...

总计: 30 个因子

## 注意事项
1. 确保所有必要的数据字段都已加载
2. 数据维度必须一致
3. 建议在生产环境中启用性能统计来监控计算性能
4. 大量因子计算时建议使用多线程

## 性能优化建议
1. 使用多线程计算：`engine.setNumThreads(8)`
2. 启用性能统计：`engine.enablePerformanceStats(true)`
3. 预热计算引擎：`engine.warmUp()`
4. 批量计算相关因子以提高缓存效率

## 故障排除
如果遇到编译或运行问题，请检查：
1. feature_operators库是否正确安装
2. 所有依赖的数据字段是否已加载
3. 数据格式是否正确
4. 内存是否充足

更多信息请参考factor_framework文档。
